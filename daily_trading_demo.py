#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
逐日交易策略演示
展示如何使用重构后的SmartTradingStrategy进行实时交易决策
"""

import sys
import os
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from points import StockTrendCalculator

def simulate_daily_trading():
    """
    模拟逐日交易过程
    """
    print("=== 逐日交易策略演示 ===\n")
    
    # 创建交易系统
    calculator = StockTrendCalculator(
        window_size=30,           # 30天窗口
        turn_point_threshold=6.0, # 6%转折点阈值
        use_advanced_detector=True,
        enable_trading=True
    )
    
    strategy = calculator.trading_strategy
    
    print("交易系统初始化完成:")
    print(f"  初始资金: {strategy.cash:,.2f}")
    print(f"  买入阈值: {strategy.buy_threshold*100:.1f}%")
    print(f"  卖出阈值: {strategy.sell_threshold*100:.1f}%")
    print(f"  止损阈值: {strategy.stop_loss*100:.1f}%")
    print()
    
    # 模拟股票数据 - 一个完整的牛熊周期
    stock_data = [
        # 第一阶段：上涨趋势
        (100, "市场开始上涨"),
        (103, "继续上涨"),
        (106, "强势上涨"),
        (104, "小幅回调"),  # 可能的买入点
        (108, "突破新高"),
        (111, "持续上涨"),
        (109, "技术性回调"),
        (113, "再创新高"),
        (116, "强劲上涨"),
        (114, "获利回吐"),
        
        # 第二阶段：高位震荡
        (117, "冲击高点"),
        (120, "创出新高"),  # 可能的卖出点
        (118, "高位震荡"),
        (122, "再次冲高"),
        (119, "震荡回落"),
        (124, "突破前高"),
        (121, "回调整理"),
        (125, "继续上攻"),
        (128, "创历史新高"),
        (126, "高位调整"),
        
        # 第三阶段：开始下跌
        (123, "开始走弱"),
        (120, "跌破支撑"),
        (117, "继续下跌"),
        (114, "加速下跌"),  # 可能触发止损
        (111, "恐慌性下跌"),
        (108, "持续下跌"),
        (105, "深度调整"),
        (102, "大幅下跌"),
        (99, "跌破百元"),
        (96, "继续下探"),
        
        # 第四阶段：筑底反弹
        (93, "接近底部"),
        (90, "创出新低"),
        (93, "开始反弹"),  # 可能的买入点
        (96, "反弹确认"),
        (99, "收复失地"),
        (102, "继续反弹"),
        (105, "强势反弹"),
        (108, "反弹加速"),
        (111, "恢复上涨"),
        (114, "重回上升通道")
    ]
    
    print("开始逐日交易模拟:\n")
    
    for day, (price, comment) in enumerate(stock_data, 1):
        date = datetime(2024, 1, 1) + timedelta(days=day-1)
        date_str = date.strftime('%Y-%m-%d')
        
        # 生成当日的高低价
        high = price * 1.015  # 高价比收盘价高1.5%
        low = price * 0.985   # 低价比收盘价低1.5%
        
        # 添加当日数据
        calculator.add_record(
            timestamp=date_str,
            high=high,
            low=low,
            close=price
        )
        
        # 检查是否有新信号
        current_signals = len(strategy.daily_signals)
        
        # 显示当日状态
        position_info = ""
        if strategy.position:
            pos = strategy.position
            current_value = pos['quantity'] * price
            profit_loss = current_value - pos['cost']
            profit_pct = profit_loss / pos['cost'] * 100
            position_info = f"持仓: {pos['quantity']}股@{pos['entry_price']:.2f} (盈亏: {profit_pct:+.1f}%)"
        else:
            position_info = "持仓: 无"
        
        print(f"第{day:2d}天 {date_str}: 价格={price:6.2f} | {position_info} | 总价值={strategy.portfolio_value:8.0f} | {comment}")
        
        # 如果有新信号，显示详情
        if current_signals > 0:
            latest_signal = strategy.daily_signals[-1]
            if latest_signal.date == date_str:
                signal_type = "🟢买入" if latest_signal.signal_type == 'BUY' else "🔴卖出"
                print(f"         >>> {signal_type}信号: {latest_signal.price:.2f} - {latest_signal.reason}")
        
        print()
    
    # 最终统计
    print("=== 交易结果统计 ===")
    performance = strategy.calculate_performance()
    
    print(f"交易周期: {len(stock_data)} 天")
    print(f"价格变化: {stock_data[0][0]:.2f} -> {stock_data[-1][0]:.2f} ({(stock_data[-1][0]/stock_data[0][0]-1)*100:+.1f}%)")
    print(f"策略收益: {performance['total_return']:+.1f}%")
    print(f"完成交易: {performance['total_trades']} 笔")
    print(f"交易胜率: {performance['win_rate']:.1f}%")
    print(f"最大回撤: {performance['max_drawdown']:.1f}%")
    print(f"最终价值: {performance['portfolio_value']:,.2f}")
    print(f"最终现金: {performance['cash']:,.2f}")
    
    if strategy.position:
        pos = strategy.position
        print(f"当前持仓: {pos['quantity']}股 @ {pos['entry_price']:.2f}")
    
    print(f"\n=== 详细交易记录 ===")
    for i, trade in enumerate(strategy.trade_history, 1):
        if trade['type'] == 'BUY':
            print(f"{i:2d}. {trade['date']} 买入 {trade['quantity']:3d}股 @ {trade['price']:6.2f} 金额: {trade['amount']:8.0f}")
        else:
            profit_pct = trade.get('profit_ratio', 0) * 100
            print(f"{i:2d}. {trade['date']} 卖出 {trade['quantity']:3d}股 @ {trade['price']:6.2f} 盈亏: {profit_pct:+5.1f}%")
        print(f"     原因: {trade['reason']}")
    
    return calculator, strategy

def show_strategy_advantages():
    """
    展示新策略的优势
    """
    print("\n=== 逐日分析策略的优势 ===")
    print("1. 实时决策:")
    print("   ✓ 每日处理新数据时立即生成交易信号")
    print("   ✓ 不依赖未来数据，符合实际交易环境")
    print("   ✓ 可以及时响应市场变化")
    
    print("\n2. 完整的头寸管理:")
    print("   ✓ 实时跟踪持仓状态和盈亏")
    print("   ✓ 自动执行止损止盈策略")
    print("   ✓ 动态调整仓位大小")
    
    print("\n3. 详细的交易记录:")
    print("   ✓ 记录每笔交易的完整信息")
    print("   ✓ 实时计算策略表现指标")
    print("   ✓ 支持策略优化和回测分析")
    
    print("\n4. 灵活的配置:")
    print("   ✓ 可调整买卖阈值和止损参数")
    print("   ✓ 支持不同的转折点检测算法")
    print("   ✓ 可以轻松集成到实盘交易系统")

if __name__ == "__main__":
    try:
        # 运行交易演示
        calculator, strategy = simulate_daily_trading()
        
        # 展示策略优势
        show_strategy_advantages()
        
        print("\n=== 演示完成 ===")
        print("SmartTradingStrategy 已成功重构为逐日分析模式！")
        print("现在可以用于实时交易决策和头寸管理。")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
