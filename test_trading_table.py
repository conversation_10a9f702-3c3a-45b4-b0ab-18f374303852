#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试交易明细表格功能
"""

import numpy as np
from points import create_interactive_chart

def create_test_data():
    """创建测试数据，包含明显的买卖信号"""
    np.random.seed(42)
    
    # 创建一个有明显趋势和转折点的价格序列
    dates = []
    prices = []
    
    # 基础价格
    base_price = 100.0
    current_price = base_price
    
    # 生成60天的数据，包含明显的上升、下降趋势
    for i in range(60):
        date = f"2024-{1 + i//30:02d}-{1 + i%30:02d}"
        dates.append(date)
        
        # 创建趋势性变化
        if i < 15:  # 前15天：上升趋势
            trend = 0.02
        elif i < 30:  # 15-30天：下降趋势
            trend = -0.025
        elif i < 45:  # 30-45天：上升趋势
            trend = 0.03
        else:  # 45-60天：震荡
            trend = 0.001
        
        # 添加随机波动
        daily_change = trend + np.random.normal(0, 0.01)
        current_price *= (1 + daily_change)
        
        # 确保价格合理
        current_price = max(current_price, base_price * 0.5)
        current_price = min(current_price, base_price * 2.0)
        
        prices.append(current_price)
    
    # 构建OHLC数据
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        # 生成OHLC数据
        volatility = 0.02
        high = close * (1 + np.random.uniform(0, volatility))
        low = close * (1 - np.random.uniform(0, volatility))
        open_price = close * (1 + np.random.uniform(-volatility/2, volatility/2))
        
        data.append({
            'date': date,
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close, 2)
        })
    
    return data

def main():
    """主函数"""
    print("🧪 测试交易明细表格功能")
    print("=" * 50)
    
    # 创建测试数据
    print("📊 创建测试数据...")
    test_data = create_test_data()
    print(f"生成了 {len(test_data)} 天的测试数据")
    print(f"价格范围: {test_data[0]['close']:.2f} -> {test_data[-1]['close']:.2f}")
    
    # 生成交互式图表
    print("\n🎨 生成交互式图表...")
    try:
        create_interactive_chart(
            data=test_data,
            window_size=20,  # 较小的窗口以便在短数据中看到效果
            turn_point_threshold=8.0,  # 较低的阈值以便产生更多信号
            enable_trading=True
        )
        print("✅ 交互式图表生成成功！")
        print("📁 文件保存为: interactive_stock_analysis.html")
        
        print("\n🎯 使用说明:")
        print("1. 打开生成的HTML文件")
        print("2. 点击 '添加下一个交易日' 按钮逐日添加数据")
        print("3. 观察K线图下方的交易明细表格")
        print("4. 表格会随着交易信号的产生而动态更新")
        print("5. 可以使用 '自动播放' 功能快速查看完整过程")
        
        print("\n📋 表格功能特点:")
        print("✓ 实时显示买卖交易记录")
        print("✓ 包含交易序号、日期、类型、价格等详细信息")
        print("✓ 显示盈亏金额和盈亏率")
        print("✓ 买入信号显示为绿色，卖出信号显示为红色")
        print("✓ 盈利显示为绿色，亏损显示为红色")
        print("✓ 自动滚动到最新交易记录")
        
    except Exception as e:
        print(f"❌ 生成图表时出错: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 测试完成！请打开HTML文件查看交易明细表格效果。")
    else:
        print("\n❌ 测试失败！")
