<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易可视化测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .trading-table { margin-top: 20px; }
        .trading-table table { width: 100%; border-collapse: collapse; }
        .trading-table th, .trading-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        .trading-table th { background-color: #f8f9fa; }
        .profit-positive { color: #28a745; font-weight: bold; }
        .profit-negative { color: #dc3545; font-weight: bold; }
        .pair-badge { background-color: #007bff; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px; }
        #equityChart { border: 1px solid #ddd; margin: 20px 0; }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>交易可视化测试</h1>
        
        <div class="test-section">
            <h2>测试说明</h2>
            <p>测试新的交易明细表格和净值曲线功能：</p>
            <ul>
                <li>✅ 交易明细表格只在卖出时产生记录</li>
                <li>✅ 每条记录包含完整的买入卖出信息</li>
                <li>✅ 净值曲线实时更新</li>
            </ul>
            
            <button onclick="simulateTrading()">模拟交易过程</button>
            <button onclick="resetTest()">重置测试</button>
        </div>

        <!-- 净值曲线图表 -->
        <div id="equityChart" style="width: 100%; height: 300px;"></div>

        <!-- 交易明细表格 -->
        <div class="trading-table">
            <h3>📊 完整交易记录</h3>
            <table>
                <thead>
                    <tr>
                        <th>交易对</th>
                        <th>买入日期</th>
                        <th>买入价格</th>
                        <th>卖出日期</th>
                        <th>卖出价格</th>
                        <th>数量</th>
                        <th>买入金额</th>
                        <th>卖出金额</th>
                        <th>盈亏</th>
                        <th>盈亏率</th>
                        <th>持有天数</th>
                    </tr>
                </thead>
                <tbody id="tradingTableBody">
                    <tr>
                        <td colspan="11">暂无完整交易记录，点击"模拟交易过程"开始测试</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 测试数据
        let completedTrades = [];
        let equityData = [];
        let cash = 100000;
        let initialCash = 100000;
        let equityChart = echarts.init(document.getElementById('equityChart'));

        function simulateTrading() {
            // 重置数据
            completedTrades = [];
            equityData = [];
            cash = 100000;

            // 模拟交易序列
            const trades = [
                { buyDate: '2024-01-01', buyPrice: 100, sellDate: '2024-01-05', sellPrice: 110, quantity: 1000 },
                { buyDate: '2024-01-06', buyPrice: 108, sellDate: '2024-01-12', sellPrice: 95, quantity: 1018 },
                { buyDate: '2024-01-13', buyPrice: 96, sellDate: '2024-01-20', sellPrice: 105, quantity: 1157 },
                { buyDate: '2024-01-21', buyPrice: 103, sellDate: '2024-01-28', sellPrice: 118, quantity: 1213 }
            ];

            // 生成净值数据点
            const allDates = [];
            trades.forEach(trade => {
                const buyDate = new Date(trade.buyDate);
                const sellDate = new Date(trade.sellDate);
                for (let d = new Date(buyDate); d <= sellDate; d.setDate(d.getDate() + 1)) {
                    allDates.push(d.toISOString().split('T')[0]);
                }
            });
            const uniqueDates = [...new Set(allDates)].sort();

            let currentEquity = cash;
            let currentPosition = null;

            // 模拟每日净值变化
            uniqueDates.forEach((date, index) => {
                // 检查是否有买入
                const buyTrade = trades.find(t => t.buyDate === date);
                if (buyTrade && !currentPosition) {
                    const buyAmount = buyTrade.quantity * buyTrade.buyPrice;
                    cash -= buyAmount;
                    currentPosition = {
                        quantity: buyTrade.quantity,
                        entryPrice: buyTrade.buyPrice,
                        entryAmount: buyAmount,
                        entryDate: date
                    };
                }

                // 检查是否有卖出
                const sellTrade = trades.find(t => t.sellDate === date);
                if (sellTrade && currentPosition) {
                    const sellAmount = currentPosition.quantity * sellTrade.sellPrice;
                    cash += sellAmount;
                    
                    // 创建完整交易记录
                    const profit = sellAmount - currentPosition.entryAmount;
                    const profitRate = (profit / currentPosition.entryAmount) * 100;
                    const holdDays = Math.ceil((new Date(date) - new Date(currentPosition.entryDate)) / (1000 * 60 * 60 * 24));
                    
                    completedTrades.push({
                        pairId: completedTrades.length + 1,
                        buyDate: currentPosition.entryDate,
                        buyPrice: currentPosition.entryPrice,
                        sellDate: date,
                        sellPrice: sellTrade.sellPrice,
                        quantity: currentPosition.quantity,
                        buyAmount: currentPosition.entryAmount,
                        sellAmount: sellAmount,
                        profit: profit,
                        profitRate: profitRate,
                        holdDays: holdDays
                    });
                    
                    currentPosition = null;
                }

                // 计算当日净值
                currentEquity = cash;
                if (currentPosition) {
                    // 模拟价格波动
                    const priceChange = (Math.random() - 0.5) * 0.1; // ±5%的随机波动
                    const currentPrice = currentPosition.entryPrice * (1 + priceChange);
                    currentEquity += currentPosition.quantity * currentPrice;
                }

                equityData.push({
                    date: date,
                    equity: currentEquity
                });
            });

            updateTradingTable();
            updateEquityChart();
        }

        function resetTest() {
            completedTrades = [];
            equityData = [];
            cash = 100000;
            updateTradingTable();
            updateEquityChart();
        }

        function updateTradingTable() {
            const tableBody = document.getElementById('tradingTableBody');

            if (completedTrades.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="11">暂无完整交易记录，点击"模拟交易过程"开始测试</td></tr>';
                return;
            }

            let html = '';
            completedTrades.forEach(trade => {
                const profitClass = trade.profit > 0 ? 'profit-positive' : 'profit-negative';

                html += `
                    <tr>
                        <td><span class="pair-badge">P${trade.pairId}</span></td>
                        <td>${trade.buyDate}</td>
                        <td>${trade.buyPrice.toFixed(2)}</td>
                        <td>${trade.sellDate}</td>
                        <td>${trade.sellPrice.toFixed(2)}</td>
                        <td>${trade.quantity}</td>
                        <td>${trade.buyAmount.toFixed(2)}</td>
                        <td>${trade.sellAmount.toFixed(2)}</td>
                        <td class="${profitClass}">${trade.profit > 0 ? '+' : ''}${trade.profit.toFixed(2)}</td>
                        <td class="${profitClass}">${trade.profitRate > 0 ? '+' : ''}${trade.profitRate.toFixed(2)}%</td>
                        <td>${trade.holdDays}</td>
                    </tr>
                `;
            });

            tableBody.innerHTML = html;
        }

        function updateEquityChart() {
            if (equityData.length === 0) {
                equityChart.clear();
                return;
            }

            const dates = equityData.map(d => d.date);
            const equityValues = equityData.map(d => d.equity);

            const option = {
                title: {
                    text: '账户净值曲线',
                    left: 'center',
                    textStyle: { fontSize: 14 }
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        const date = params[0].axisValue;
                        const equity = params[0].value;
                        const returnRate = ((equity - initialCash) / initialCash * 100).toFixed(2);
                        return `日期: ${date}<br/>净值: ${equity.toFixed(2)}<br/>收益率: ${returnRate}%`;
                    }
                },
                grid: { left: '3%', right: '4%', bottom: '3%', top: '15%', containLabel: true },
                xAxis: {
                    type: 'category',
                    data: dates,
                    boundaryGap: false
                },
                yAxis: {
                    type: 'value',
                    scale: true,
                    name: '净值',
                    nameLocation: 'middle',
                    nameGap: 40
                },
                series: [{
                    name: '净值',
                    type: 'line',
                    data: equityValues,
                    smooth: true,
                    symbol: 'none',
                    lineStyle: { color: '#1f77b4', width: 2 },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                { offset: 0, color: 'rgba(31, 119, 180, 0.3)' },
                                { offset: 1, color: 'rgba(31, 119, 180, 0.1)' }
                            ]
                        }
                    }
                }]
            };

            equityChart.setOption(option);
        }

        // 初始化
        updateEquityChart();
    </script>
</body>
</html>
