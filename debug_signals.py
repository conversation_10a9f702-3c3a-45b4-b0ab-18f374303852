#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试信号生成问题
"""

import numpy as np
from points import create_interactive_chart

def create_debug_data():
    """创建用于调试的测试数据，确保有明显的买卖信号"""
    
    # 创建一个简单的价格序列，有明显的上升和下降趋势
    dates = []
    prices = []
    
    # 基础价格
    base_price = 100.0
    
    # 生成20天的数据，有明显的波峰波谷
    for i in range(20):
        date = f"2024-01-{i+1:02d}"
        dates.append(date)
        
        # 创建明显的锯齿状价格变化
        if i % 4 == 0:  # 每4天一个波谷
            price = base_price - 10
        elif i % 4 == 2:  # 每4天一个波峰
            price = base_price + 10
        else:
            price = base_price
        
        # 添加一些随机波动
        price += np.random.uniform(-2, 2)
        prices.append(price)
    
    # 构建OHLC数据
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        # 生成简单的OHLC数据
        high = close + 1
        low = close - 1
        open_price = close + np.random.uniform(-0.5, 0.5)
        
        data.append({
            'date': date,
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close, 2)
        })
    
    return data

def create_debug_html():
    """创建带调试信息的HTML文件"""
    
    test_data = create_debug_data()
    
    # 显示价格数据
    print("📊 测试数据:")
    for i, record in enumerate(test_data):
        print(f"  第{i+1}天 ({record['date']}): {record['close']:.2f}")
    
    # 生成HTML，添加调试代码
    html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>交易信号调试</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        #chart {{ width: 100%; height: 400px; }}
        #debug {{ margin-top: 20px; padding: 10px; background: #f5f5f5; }}
        .debug-section {{ margin: 10px 0; }}
        button {{ margin: 5px; padding: 10px; }}
    </style>
</head>
<body>
    <h1>🔍 交易信号调试</h1>
    
    <div id="chart"></div>
    
    <div>
        <button onclick="addNextDay()">添加下一个交易日</button>
        <button onclick="showDebugInfo()">显示调试信息</button>
        <button onclick="testSignalGeneration()">测试信号生成</button>
    </div>
    
    <div id="debug">
        <div class="debug-section">
            <h3>📊 当前状态</h3>
            <div id="status">等待开始...</div>
        </div>
        
        <div class="debug-section">
            <h3>🎯 转折点信息</h3>
            <div id="turnpoints">无转折点</div>
        </div>
        
        <div class="debug-section">
            <h3>📈 交易信号</h3>
            <div id="signals">无信号</div>
        </div>
        
        <div class="debug-section">
            <h3>💰 交易记录</h3>
            <div id="trades">无交易记录</div>
        </div>
    </div>

    <script>
        // 股票数据
        const stockData = {test_data};
        
        // 转折点检测器
        class EnhancedTurnPointDetector {{
            constructor(threshold) {{
                this.threshold = threshold / 100.0;
                this.turn_points = [];
            }}

            addRecord(date, high, low, close) {{
                const record = {{ date, high, low, close, dayIndex: this.turn_points.length }};
                
                // 简化的转折点检测：每3个点检测一次
                if (this.turn_points.length >= 2) {{
                    const prev2 = this.turn_points[this.turn_points.length - 2];
                    const prev1 = this.turn_points[this.turn_points.length - 1];
                    
                    // 检测波峰：中间点高于两边
                    if (prev1.close > prev2.close && prev1.close > close) {{
                        const change = Math.abs(prev1.close - prev2.close) / prev2.close;
                        if (change >= this.threshold) {{
                            prev1.pointType = 'PEAK';
                            prev1.price = prev1.close;
                            console.log(`🔴 检测到波峰: 第${{prev1.dayIndex}}天, 价格: ${{prev1.price}}`);
                        }}
                    }}
                    
                    // 检测波谷：中间点低于两边
                    if (prev1.close < prev2.close && prev1.close < close) {{
                        const change = Math.abs(prev2.close - prev1.close) / prev2.close;
                        if (change >= this.threshold) {{
                            prev1.pointType = 'TROUGH';
                            prev1.price = prev1.close;
                            console.log(`🟢 检测到波谷: 第${{prev1.dayIndex}}天, 价格: ${{prev1.price}}`);
                        }}
                    }}
                }}
                
                this.turn_points.push(record);
            }}

            getPeaks() {{ 
                const peaks = this.turn_points.filter(p => p.pointType === 'PEAK');
                console.log(`📊 当前波峰数量: ${{peaks.length}}`);
                return peaks;
            }}
            
            getTroughs() {{ 
                const troughs = this.turn_points.filter(t => t.pointType === 'TROUGH');
                console.log(`📊 当前波谷数量: ${{troughs.length}}`);
                return troughs;
            }}
        }}

        // 交易策略
        class SmartTradingStrategy {{
            constructor(buyThreshold = 5.0, sellThreshold = 10.0) {{
                this.buyThreshold = buyThreshold / 100.0;
                this.sellThreshold = sellThreshold / 100.0;
                this.signals = [];
                console.log(`📋 交易策略初始化: 买入阈值=${{buyThreshold}}%, 卖出阈值=${{sellThreshold}}%`);
            }}

            analyzeSignals(calculator) {{
                const peaks = calculator.turnPointDetector.getPeaks();
                const troughs = calculator.turnPointDetector.getTroughs();
                
                console.log(`🔍 分析信号: 波峰=${{peaks.length}}个, 波谷=${{troughs.length}}个`);

                // 生成买入信号 - 简化版：所有波谷都是买入信号
                for (let trough of troughs) {{
                    const existingSignal = this.signals.find(s => 
                        s.dayIndex === trough.dayIndex && s.signalType === 'BUY'
                    );
                    
                    if (!existingSignal) {{
                        this.signals.push({{
                            date: trough.date,
                            price: trough.price,
                            signalType: 'BUY',
                            dayIndex: trough.dayIndex,
                            reason: '波谷买入信号',
                            confidence: 0.8
                        }});
                        console.log(`✅ 生成买入信号: 第${{trough.dayIndex}}天, 价格: ${{trough.price}}`);
                    }}
                }}

                // 生成卖出信号 - 简化版：所有波峰都是卖出信号
                for (let peak of peaks) {{
                    const existingSignal = this.signals.find(s => 
                        s.dayIndex === peak.dayIndex && s.signalType === 'SELL'
                    );
                    
                    if (!existingSignal) {{
                        this.signals.push({{
                            date: peak.date,
                            price: peak.price,
                            signalType: 'SELL',
                            dayIndex: peak.dayIndex,
                            reason: '波峰卖出信号',
                            confidence: 0.8
                        }});
                        console.log(`✅ 生成卖出信号: 第${{peak.dayIndex}}天, 价格: ${{peak.price}}`);
                    }}
                }}

                console.log(`📊 总信号数: ${{this.signals.length}}个`);
                return this.signals;
            }}

            getBuySignals() {{ 
                const buySignals = this.signals.filter(s => s.signalType === 'BUY');
                console.log(`🟢 买入信号: ${{buySignals.length}}个`);
                return buySignals;
            }}
            
            getSellSignals() {{ 
                const sellSignals = this.signals.filter(s => s.signalType === 'SELL');
                console.log(`🔴 卖出信号: ${{sellSignals.length}}个`);
                return sellSignals;
            }}
        }}

        // 计算器
        class StockTrendCalculator {{
            constructor(windowSize, turnPointThreshold) {{
                this.windowSize = windowSize;
                this.records = [];
                this.dayCounter = 0;
                this.turnPointDetector = new EnhancedTurnPointDetector(turnPointThreshold);
                this.tradingStrategy = new SmartTradingStrategy(5.0, 10.0);
                console.log(`🚀 计算器初始化: 窗口=${{windowSize}}, 阈值=${{turnPointThreshold}}%`);
            }}

            addRecord(date, high, low, close) {{
                this.records.push({{ date, high, low, close, dayIndex: this.dayCounter }});
                this.turnPointDetector.addRecord(date, high, low, close);
                this.dayCounter++;
                
                // 分析信号
                this.tradingStrategy.analyzeSignals(this);
                
                console.log(`📅 添加记录: ${{date}}, 收盘价: ${{close}}, 总天数: ${{this.dayCounter}}`);
            }}

            getBuySignals() {{ return this.tradingStrategy.getBuySignals(); }}
            getSellSignals() {{ return this.tradingStrategy.getSellSignals(); }}
        }}

        // 初始化
        const calculator = new StockTrendCalculator(3, 5.0);
        let currentIndex = 0;
        let tradingRecords = [];
        let currentPosition = null;
        let cash = 100000;

        // 图表初始化
        const chart = echarts.init(document.getElementById('chart'));

        function updateChart() {{
            const displayData = stockData.slice(0, currentIndex);
            
            const option = {{
                title: {{ text: '股票价格调试图表' }},
                xAxis: {{ 
                    type: 'category',
                    data: displayData.map(d => d.date)
                }},
                yAxis: {{ type: 'value' }},
                series: [{{
                    name: '收盘价',
                    type: 'line',
                    data: displayData.map(d => d.close)
                }}]
            }};
            
            chart.setOption(option);
        }}

        function addNextDay() {{
            if (currentIndex >= stockData.length) {{
                document.getElementById('status').textContent = '所有数据已添加完毕！';
                return;
            }}
            
            const record = stockData[currentIndex];
            calculator.addRecord(record.date, record.high, record.low, record.close);
            currentIndex++;
            
            updateChart();
            updateDebugInfo();
            
            console.log(`🎯 当前进度: ${{currentIndex}}/${{stockData.length}}`);
        }}

        function updateDebugInfo() {{
            const peaks = calculator.turnPointDetector.getPeaks();
            const troughs = calculator.turnPointDetector.getTroughs();
            const buySignals = calculator.getBuySignals();
            const sellSignals = calculator.getSellSignals();
            
            document.getElementById('status').innerHTML = `
                当前天数: ${{currentIndex}}<br>
                现金: ${{cash.toFixed(2)}}<br>
                持仓: ${{currentPosition ? '有' : '无'}}
            `;
            
            document.getElementById('turnpoints').innerHTML = `
                波峰: ${{peaks.length}}个<br>
                波谷: ${{troughs.length}}个<br>
                详情: ${{peaks.map(p => `第${{p.dayIndex}}天(${{p.price}})`).join(', ')}}
            `;
            
            document.getElementById('signals').innerHTML = `
                买入信号: ${{buySignals.length}}个<br>
                卖出信号: ${{sellSignals.length}}个<br>
                买入详情: ${{buySignals.map(s => `第${{s.dayIndex}}天(${{s.price}})`).join(', ')}}<br>
                卖出详情: ${{sellSignals.map(s => `第${{s.dayIndex}}天(${{s.price}})`).join(', ')}}
            `;
            
            document.getElementById('trades').innerHTML = `
                交易记录: ${{tradingRecords.length}}条<br>
                详情: ${{tradingRecords.map(t => `${{t.date}} ${{t.type}} ${{t.price}}`).join('<br>')}}
            `;
        }}

        function showDebugInfo() {{
            console.log('=== 调试信息 ===');
            console.log('当前索引:', currentIndex);
            console.log('转折点:', calculator.turnPointDetector.turn_points);
            console.log('买入信号:', calculator.getBuySignals());
            console.log('卖出信号:', calculator.getSellSignals());
            console.log('交易记录:', tradingRecords);
        }}

        function testSignalGeneration() {{
            console.log('=== 测试信号生成 ===');
            
            // 手动添加几天数据
            for (let i = 0; i < Math.min(10, stockData.length); i++) {{
                addNextDay();
            }}
            
            showDebugInfo();
        }}

        // 初始化图表
        updateChart();
        updateDebugInfo();
        
        console.log('🎉 调试页面加载完成！');
    </script>
</body>
</html>
    """
    
    with open('debug_signals.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("✅ 调试HTML文件已生成: debug_signals.html")

def main():
    """主函数"""
    print("🔍 调试交易信号生成")
    print("=" * 50)
    
    create_debug_html()
    
    print("\n🎯 调试步骤:")
    print("1. 打开 debug_signals.html 文件")
    print("2. 打开浏览器开发者工具 (F12)")
    print("3. 点击 '测试信号生成' 按钮")
    print("4. 观察控制台输出和调试信息")
    print("5. 检查是否有转折点和信号生成")
    
    print("\n🔍 预期结果:")
    print("- 应该检测到波峰和波谷")
    print("- 应该生成买入和卖出信号")
    print("- 控制台应该有详细的调试输出")

if __name__ == "__main__":
    main()
