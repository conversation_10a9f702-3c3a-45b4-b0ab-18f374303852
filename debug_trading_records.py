#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试交易记录显示问题
"""

import numpy as np
from points import create_interactive_chart

def create_simple_test_data():
    """创建简单的测试数据，确保有明显的买卖信号"""
    
    # 创建一个简单的价格序列，有明显的上升和下降趋势
    dates = []
    prices = []
    
    # 基础价格
    base_price = 100.0
    
    # 生成30天的数据
    for i in range(30):
        date = f"2024-01-{i+1:02d}"
        dates.append(date)
        
        # 创建明显的趋势变化
        if i < 10:  # 前10天：下降趋势（应该产生买入信号）
            price = base_price - i * 2
        elif i < 20:  # 10-20天：上升趋势（应该产生卖出信号）
            price = base_price - 20 + (i - 10) * 3
        else:  # 20-30天：再次下降
            price = base_price + 10 - (i - 20) * 1.5
        
        prices.append(price)
    
    # 构建OHLC数据
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        # 生成简单的OHLC数据
        high = close + 1
        low = close - 1
        open_price = close + np.random.uniform(-0.5, 0.5)
        
        data.append({
            'date': date,
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close, 2)
        })
    
    return data

def main():
    """主函数"""
    print("🔍 调试交易记录显示问题")
    print("=" * 50)
    
    # 创建简单测试数据
    print("📊 创建简单测试数据...")
    test_data = create_simple_test_data()
    print(f"生成了 {len(test_data)} 天的测试数据")
    
    # 显示价格趋势
    print("\n📈 价格趋势:")
    for i, record in enumerate(test_data):
        if i % 5 == 0:  # 每5天显示一次
            print(f"  第{i+1}天 ({record['date']}): {record['close']:.2f}")
    
    # 生成交互式图表
    print(f"\n🎨 生成交互式图表...")
    print(f"使用参数: window_size=5, turn_point_threshold=5.0")
    
    try:
        create_interactive_chart(
            data=test_data,
            window_size=5,  # 小窗口
            turn_point_threshold=5.0,  # 低阈值，容易产生信号
            enable_trading=True
        )
        print("✅ 交互式图表生成成功！")
        print("📁 文件保存为: interactive_stock_analysis.html")
        
        print("\n🎯 调试步骤:")
        print("1. 打开生成的HTML文件")
        print("2. 打开浏览器开发者工具 (F12)")
        print("3. 点击 '添加下一个交易日' 按钮")
        print("4. 在控制台查看是否有JavaScript错误")
        print("5. 观察交易明细表格是否有记录出现")
        
        print("\n🔍 预期结果:")
        print("- 前10天价格下降，应该在波谷产生买入信号")
        print("- 10-20天价格上升，应该在波峰产生卖出信号")
        print("- 交易明细表格应该显示买卖记录")
        
        print("\n⚠️  如果没有交易记录:")
        print("- 检查转折点阈值是否过高")
        print("- 检查窗口大小是否合适")
        print("- 查看浏览器控制台的JavaScript错误")
        
    except Exception as e:
        print(f"❌ 生成图表时出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 调试脚本执行完成！请按照上述步骤检查交易记录显示。")
    else:
        print("\n❌ 调试脚本执行失败！")
