#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试止损止盈功能
"""

import pandas as pd
import numpy as np
from points import StockTrendCalculator, SmartTradingStrategy

def create_test_data():
    """创建测试数据 - 模拟一个先上涨后下跌的场景来测试止损"""
    dates = pd.date_range('2024-01-01', periods=50, freq='D')
    
    # 创建一个先上涨20%然后下跌15%的价格序列
    base_price = 100.0
    prices = []
    
    # 前20天上涨到120
    for i in range(20):
        price = base_price + (20 * i / 19)  # 从100涨到120
        prices.append(price)
    
    # 后30天从120跌到102（下跌15%）
    for i in range(30):
        price = 120 - (18 * i / 29)  # 从120跌到102
        prices.append(price)
    
    data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        # 添加一些随机波动
        high = price * (1 + np.random.uniform(0, 0.02))
        low = price * (1 - np.random.uniform(0, 0.02))
        close = price
        
        data.append({
            'date': date.strftime('%Y-%m-%d'),
            'high': high,
            'low': low,
            'close': close,
            'day_index': i
        })
    
    return data

def test_stop_loss_functionality():
    """测试止损止盈功能"""
    print("🧪 开始测试止损止盈功能...")
    
    # 创建测试数据
    test_data = create_test_data()
    
    # 创建计算器和策略
    calculator = StockTrendCalculator(window_size=30, turn_point_threshold=10.0)
    
    # 设置较严格的止损止盈参数进行测试
    strategy = SmartTradingStrategy(
        buy_threshold=8.0,      # 买入阈值
        sell_threshold=15.0,    # 卖出阈值  
        stop_loss=5.0,          # 5%止损
        take_profit=15.0,       # 15%止盈
        trailing_stop=3.0       # 3%移动止损
    )
    
    calculator.trading_strategy = strategy
    
    print(f"📊 策略参数:")
    print(f"   - 买入阈值: {strategy.buy_threshold*100:.1f}%")
    print(f"   - 卖出阈值: {strategy.sell_threshold*100:.1f}%") 
    print(f"   - 固定止损: {strategy.stop_loss*100:.1f}%")
    print(f"   - 止盈: {strategy.take_profit*100:.1f}%")
    print(f"   - 移动止损: {strategy.trailing_stop*100:.1f}%")
    print()
    
    # 逐日处理数据
    for i, record in enumerate(test_data):
        calculator.add_record(record['date'], record['high'], record['low'], record['close'])
        
        # 处理交易信号
        daily_signals = strategy.process_daily_data(calculator, record)
        
        if daily_signals:
            for signal in daily_signals:
                print(f"📅 第{i+1}天 ({record['date']}): {signal.signal_type} @ {signal.price:.2f} - {signal.reason}")
        
        # 显示当前状态
        if strategy.position:
            current_price = record['close']
            entry_price = strategy.position['entry_price']
            unrealized_pnl = (current_price - entry_price) / entry_price * 100
            print(f"   持仓状态: {strategy.position['quantity']}股@{entry_price:.2f}, 当前价格: {current_price:.2f}, 浮盈: {unrealized_pnl:+.1f}%")
            
            if strategy.highest_price_since_buy:
                print(f"   止损跟踪: 最高价{strategy.highest_price_since_buy:.2f}, 移动止损价{strategy.trailing_stop_price:.2f}")
    
    # 显示交易结果
    print("\n📈 交易结果:")
    performance = strategy.calculate_performance()
    if performance:
        print(f"   总收益率: {performance['total_return']:.2f}%")
        print(f"   完成交易: {performance['total_trades']}笔")
        print(f"   胜率: {performance['win_rate']:.1f}%")
        print(f"   最大回撤: {performance['max_drawdown']:.2f}%")
        print(f"   当前资金: {performance['cash']:.2f}")
        if performance['current_position']:
            print(f"   当前持仓: {performance['current_position']['quantity']}股@{performance['current_position']['entry_price']:.2f}")
    
    # 显示详细交易记录
    print("\n📋 详细交易记录:")
    for trade in strategy.trade_history:
        print(f"   {trade['date']}: {trade['type']} {trade['quantity']}股@{trade['price']:.2f} - {trade['reason']}")

if __name__ == "__main__":
    test_stop_loss_functionality()
