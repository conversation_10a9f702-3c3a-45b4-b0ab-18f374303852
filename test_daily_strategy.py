#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试逐日信号生成功能
验证重构后的SmartTradingStrategy是否能正确进行逐日分析
"""

import sys
import os
import random
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from points import StockTrendCalculator, SmartTradingStrategy

def generate_test_data(days=100, start_price=100.0):
    """
    生成测试用的股票数据
    包含一些明显的趋势和转折点
    """
    data = []
    current_price = start_price
    start_date = datetime(2024, 1, 1)
    
    for i in range(days):
        date = start_date + timedelta(days=i)
        
        # 模拟价格波动
        if i < 20:
            # 前20天上涨趋势
            change = random.uniform(0.5, 2.0) / 100
        elif i < 40:
            # 20-40天下跌趋势
            change = random.uniform(-2.5, -0.5) / 100
        elif i < 70:
            # 40-70天震荡上涨
            change = random.uniform(-1.0, 2.5) / 100
        else:
            # 70天后大幅下跌然后反弹
            if i < 85:
                change = random.uniform(-3.0, -1.0) / 100
            else:
                change = random.uniform(1.0, 3.0) / 100
        
        current_price *= (1 + change)
        
        # 生成高低价
        daily_volatility = random.uniform(0.5, 2.0) / 100
        high = current_price * (1 + daily_volatility)
        low = current_price * (1 - daily_volatility)
        close = current_price
        
        data.append({
            'date': date.strftime('%Y-%m-%d'),
            'high': high,
            'low': low,
            'close': close
        })
    
    return data

def test_daily_strategy():
    """
    测试逐日策略功能
    """
    print("=== 测试逐日信号生成功能 ===\n")
    
    # 生成测试数据
    test_data = generate_test_data(100, 100.0)
    print(f"生成了 {len(test_data)} 天的测试数据")
    
    # 创建计算器（启用交易策略）
    calculator = StockTrendCalculator(
        window_size=60,
        turn_point_threshold=8.0,
        use_advanced_detector=True,
        enable_trading=True
    )
    
    print(f"初始策略状态:")
    strategy = calculator.trading_strategy
    print(f"  现金: {strategy.cash:.2f}")
    print(f"  组合价值: {strategy.portfolio_value:.2f}")
    print(f"  持仓: {strategy.position}")
    
    # 逐日添加数据并观察信号生成
    daily_signals_count = 0
    signal_days = []
    
    for i, record in enumerate(test_data):
        # 添加数据（这会触发逐日策略分析）
        calculator.add_record(
            timestamp=record['date'],
            high=record['high'],
            low=record['low'],
            close=record['close']
        )
        
        # 检查是否有新信号
        current_signals = len(strategy.daily_signals)
        if current_signals > daily_signals_count:
            new_signals = strategy.daily_signals[daily_signals_count:]
            daily_signals_count = current_signals
            
            print(f"\n第 {i+1} 天 ({record['date']}) 生成信号:")
            for signal in new_signals:
                print(f"  {signal.signal_type}: {signal.price:.2f} - {signal.reason}")
            
            # 显示当前策略状态
            print(f"  当前状态:")
            print(f"    现金: {strategy.cash:.2f}")
            print(f"    组合价值: {strategy.portfolio_value:.2f}")
            if strategy.position:
                print(f"    持仓: {strategy.position['type']} {strategy.position['quantity']}股 @ {strategy.position['entry_price']:.2f}")
            else:
                print(f"    持仓: 无")
            
            signal_days.append(i+1)
    
    # 最终统计
    print(f"\n=== 测试结果统计 ===")
    print(f"总交易天数: {len(test_data)}")
    print(f"产生信号的天数: {len(signal_days)}")
    print(f"总信号数量: {len(strategy.daily_signals)}")
    
    # 信号类型统计
    buy_signals = [s for s in strategy.daily_signals if s.signal_type == 'BUY']
    sell_signals = [s for s in strategy.daily_signals if s.signal_type == 'SELL']
    print(f"买入信号: {len(buy_signals)}")
    print(f"卖出信号: {len(sell_signals)}")
    
    # 交易历史
    print(f"\n=== 交易历史 ===")
    for i, trade in enumerate(strategy.trade_history):
        if trade['type'] == 'BUY':
            print(f"交易 {i+1}: {trade['date']} 买入 {trade['quantity']}股 @ {trade['price']:.2f} - {trade['reason']}")
        else:
            profit_pct = trade.get('profit_ratio', 0) * 100
            print(f"交易 {i+1}: {trade['date']} 卖出 {trade['quantity']}股 @ {trade['price']:.2f} 盈亏: {profit_pct:.1f}% - {trade['reason']}")
    
    # 策略表现
    performance = strategy.calculate_performance()
    print(f"\n=== 策略表现 ===")
    print(f"完成交易数: {performance['total_trades']}")
    print(f"胜率: {performance['win_rate']:.1f}%")
    print(f"总收益率: {performance['total_return']:.1f}%")
    print(f"平均收益率: {performance['avg_return']:.1f}%")
    print(f"最大回撤: {performance['max_drawdown']:.1f}%")
    print(f"最终组合价值: {performance['portfolio_value']:.2f}")
    print(f"最终现金: {performance['cash']:.2f}")
    
    return calculator, strategy

def test_reset_functionality():
    """
    测试策略重置功能
    """
    print("\n=== 测试策略重置功能 ===")
    
    # 创建策略并添加一些数据
    calculator = StockTrendCalculator(enable_trading=True)
    strategy = calculator.trading_strategy
    
    # 添加几天数据
    test_data = generate_test_data(10, 100.0)
    for record in test_data:
        calculator.add_record(
            timestamp=record['date'],
            high=record['high'],
            low=record['low'],
            close=record['close']
        )
    
    print(f"重置前:")
    print(f"  信号数量: {len(strategy.signals)}")
    print(f"  交易历史: {len(strategy.trade_history)}")
    print(f"  组合价值: {strategy.portfolio_value:.2f}")
    
    # 重置策略
    strategy.reset_strategy()
    
    print(f"重置后:")
    print(f"  信号数量: {len(strategy.signals)}")
    print(f"  交易历史: {len(strategy.trade_history)}")
    print(f"  组合价值: {strategy.portfolio_value:.2f}")
    print(f"  现金: {strategy.cash:.2f}")
    print(f"  持仓: {strategy.position}")

if __name__ == "__main__":
    try:
        # 测试逐日策略
        calculator, strategy = test_daily_strategy()
        
        # 测试重置功能
        test_reset_functionality()
        
        print("\n=== 所有测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
