#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试不同出场原因的显示效果
"""

import pandas as pd
import numpy as np
from points import StockTrendCalculator, SmartTradingStrategy

def create_test_scenarios():
    """创建不同的测试场景来触发各种出场原因"""
    
    scenarios = []
    
    # 场景1: 固定止损 - 价格下跌超过8%
    print("🧪 场景1: 测试固定止损")
    dates1 = pd.date_range('2024-01-01', periods=30, freq='D')
    prices1 = []
    base_price = 100.0
    
    # 前10天上涨到110
    for i in range(10):
        price = base_price + (10 * i / 9)
        prices1.append(price)
    
    # 后20天急跌到85（跌幅超过8%）
    for i in range(20):
        price = 110 - (25 * i / 19)  # 从110跌到85
        prices1.append(price)
    
    scenario1 = create_scenario_data(dates1, prices1, "固定止损场景")
    scenarios.append(("固定止损", scenario1))
    
    # 场景2: 止盈 - 价格上涨超过20%
    print("🧪 场景2: 测试止盈")
    dates2 = pd.date_range('2024-02-01', periods=25, freq='D')
    prices2 = []
    base_price = 100.0
    
    # 25天内从100涨到125（涨幅25%，触发20%止盈）
    for i in range(25):
        price = base_price + (25 * i / 24)
        prices2.append(price)
    
    scenario2 = create_scenario_data(dates2, prices2, "止盈场景")
    scenarios.append(("止盈", scenario2))
    
    # 场景3: 移动止损 - 价格先涨后跌
    print("🧪 场景3: 测试移动止损")
    dates3 = pd.date_range('2024-03-01', periods=40, freq='D')
    prices3 = []
    base_price = 100.0
    
    # 前20天涨到130
    for i in range(20):
        price = base_price + (30 * i / 19)
        prices3.append(price)
    
    # 后20天从130跌到120（触发移动止损）
    for i in range(20):
        price = 130 - (10 * i / 19)
        prices3.append(price)
    
    scenario3 = create_scenario_data(dates3, prices3, "移动止损场景")
    scenarios.append(("移动止损", scenario3))
    
    return scenarios

def create_scenario_data(dates, prices, scenario_name):
    """创建场景数据"""
    data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        # 添加一些随机波动
        high = price * (1 + np.random.uniform(0, 0.01))
        low = price * (1 - np.random.uniform(0, 0.01))
        close = price
        
        data.append({
            'date': date.strftime('%Y-%m-%d'),
            'high': high,
            'low': low,
            'close': close,
            'day_index': i
        })
    
    return data

def test_scenario(scenario_name, test_data):
    """测试单个场景"""
    print(f"\n{'='*50}")
    print(f"🎯 测试场景: {scenario_name}")
    print(f"{'='*50}")
    
    # 创建计算器和策略
    calculator = StockTrendCalculator(window_size=20, turn_point_threshold=8.0)
    
    # 设置较敏感的止损止盈参数
    strategy = SmartTradingStrategy(
        buy_threshold=5.0,      # 较低的买入阈值，容易触发买入
        sell_threshold=15.0,    # 卖出阈值
        stop_loss=8.0,          # 8%固定止损
        take_profit=20.0,       # 20%止盈
        trailing_stop=5.0       # 5%移动止损
    )
    
    calculator.trading_strategy = strategy
    
    # 逐日处理数据
    for i, record in enumerate(test_data):
        calculator.add_record(record['date'], record['high'], record['low'], record['close'])
        
        # 处理交易信号
        daily_signals = strategy.process_daily_data(calculator, record)
        
        if daily_signals:
            for signal in daily_signals:
                print(f"📅 第{i+1}天 ({record['date']}): {signal.signal_type} @ {signal.price:.2f} - {signal.reason}")
        
        # 显示当前状态
        if strategy.position:
            current_price = record['close']
            entry_price = strategy.position['entry_price']
            unrealized_pnl = (current_price - entry_price) / entry_price * 100
            print(f"   💼 持仓: {strategy.position['quantity']}股@{entry_price:.2f}, 当前: {current_price:.2f}, 浮盈: {unrealized_pnl:+.1f}%")
            
            if strategy.highest_price_since_buy:
                print(f"   📈 最高价: {strategy.highest_price_since_buy:.2f}, 移动止损价: {strategy.trailing_stop_price:.2f}")
    
    # 显示交易结果
    print(f"\n📊 {scenario_name} - 交易结果:")
    performance = strategy.calculate_performance()
    if performance:
        print(f"   总收益率: {performance['total_return']:.2f}%")
        print(f"   完成交易: {performance['total_trades']}笔")
        print(f"   胜率: {performance['win_rate']:.1f}%")
        print(f"   当前资金: {performance['cash']:.2f}")
    
    # 显示详细交易记录
    print(f"\n📋 {scenario_name} - 详细交易记录:")
    for trade in strategy.trade_history:
        print(f"   {trade['date']}: {trade['type']} {trade['quantity']}股@{trade['price']:.2f} - {trade['reason']}")
    
    return strategy.trade_history

def main():
    """主测试函数"""
    print("🚀 开始测试不同出场原因的显示效果")
    print("="*60)
    
    scenarios = create_test_scenarios()
    all_trades = []
    
    for scenario_name, test_data in scenarios:
        trades = test_scenario(scenario_name, test_data)
        all_trades.extend(trades)
    
    print(f"\n{'='*60}")
    print("🎉 所有测试场景完成！")
    print(f"总交易记录: {len(all_trades)}条")
    
    # 统计不同出场原因
    exit_reasons = {}
    for trade in all_trades:
        if trade['type'] == 'SELL':
            reason = trade['reason']
            exit_reasons[reason] = exit_reasons.get(reason, 0) + 1
    
    print("\n📈 出场原因统计:")
    for reason, count in exit_reasons.items():
        print(f"   {reason}: {count}次")

if __name__ == "__main__":
    main()
