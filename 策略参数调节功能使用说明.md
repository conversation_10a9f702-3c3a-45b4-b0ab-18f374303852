# 策略参数调节功能使用说明

## 🎯 功能概述

我们已经成功为交易可视化页面添加了**可折叠的策略参数调节面板**，让您可以在不重新运行Python代码的情况下，动态调整交易策略参数并实时查看效果。

## 📊 可调节参数

### 📈 交易信号参数
1. **买入阈值 (1-20%)**
   - 默认值: 8.0%
   - 作用: 控制在趋势回调时的买入敏感度
   - 数值越小 = 更容易触发买入信号

2. **卖出阈值 (5-30%)**
   - 默认值: 15.0%
   - 作用: 控制趋势转折时的卖出敏感度
   - 数值越小 = 更容易触发卖出信号

### 🛡️ 风险管理参数
3. **固定止损 (2-15%)**
   - 默认值: 8.0%
   - 作用: 设置最大允许亏损比例
   - 数值越小 = 风险控制越严格

4. **止盈 (10-50%)**
   - 默认值: 20.0%
   - 作用: 设置目标盈利比例
   - 数值越小 = 更保守的利润锁定

5. **移动止损 (2-10%)**
   - 默认值: 5.0%
   - 作用: 跟踪止损的回撤比例
   - 数值越小 = 更紧的利润保护

## 🖥️ 界面操作

### 打开参数面板
1. 运行 `python points.py` 生成可视化页面
2. 在浏览器中打开 `interactive_stock_analysis.html`
3. 点击 **"⚙️ 策略参数设置 ▼"** 按钮
4. 面板会展开显示所有可调节参数

### 调整参数
1. **修改数值**: 直接在输入框中修改参数值
2. **参数验证**: 系统会自动验证参数范围的合理性
3. **应用参数**: 点击 **"✅ 应用参数"** 按钮确认修改
4. **重置参数**: 点击 **"🔄 重置默认"** 按钮恢复默认值

### 查看效果
1. **应用参数后**: 系统会显示确认对话框，列出新的参数值
2. **重置图表**: 点击 **"重置图表"** 按钮，新参数将生效
3. **重新模拟**: 使用新参数重新进行交易模拟

## 🔧 使用流程

### 完整操作步骤
```
1. 打开可视化页面
   ↓
2. 点击"策略参数设置"展开面板
   ↓
3. 调整想要修改的参数
   ↓
4. 点击"应用参数"确认修改
   ↓
5. 点击"重置图表"让新参数生效
   ↓
6. 开始新的交易模拟
```

### 参数调优建议

#### 🎯 激进策略 (追求高收益)
```
买入阈值: 5.0%    (更容易买入)
卖出阈值: 10.0%   (更容易卖出)
固定止损: 6.0%    (较松的止损)
止盈: 30.0%       (更高的目标)
移动止损: 3.0%    (较紧的跟踪)
```

#### 🛡️ 保守策略 (控制风险)
```
买入阈值: 12.0%   (更谨慎买入)
卖出阈值: 20.0%   (更谨慎卖出)
固定止损: 5.0%    (严格止损)
止盈: 15.0%       (较低目标)
移动止损: 7.0%    (较松的跟踪)
```

#### ⚖️ 平衡策略 (默认推荐)
```
买入阈值: 8.0%    (适中敏感度)
卖出阈值: 15.0%   (适中敏感度)
固定止损: 8.0%    (适中风险)
止盈: 20.0%       (适中目标)
移动止损: 5.0%    (适中跟踪)
```

## 💡 使用技巧

### 参数调优策略
1. **单一变量法**: 每次只调整一个参数，观察效果
2. **对比测试**: 记录不同参数组合的收益率和胜率
3. **风险优先**: 先调整止损参数，确保风险可控
4. **逐步优化**: 从保守参数开始，逐步调整到理想状态

### 效果观察重点
- **交易频率**: 参数调整后的买卖信号数量变化
- **胜率变化**: 完成交易的盈利比例
- **最大回撤**: 账户净值的最大下跌幅度
- **出场原因**: 不同止损止盈触发的频率

## 🚨 注意事项

### 参数限制
- 所有参数都有合理的取值范围限制
- 系统会自动验证参数的有效性
- 不合理的参数会弹出警告提示

### 生效机制
- **参数修改**: 立即更新到内存中
- **实际生效**: 需要重置图表后才会应用到交易模拟
- **历史数据**: 不会影响已完成的交易记录

### 最佳实践
1. **备份记录**: 记录表现良好的参数组合
2. **渐进调整**: 避免大幅度修改参数
3. **充分测试**: 在不同市场环境下测试参数效果
4. **风险意识**: 始终关注最大回撤和止损效果

## 🎉 功能优势

### 🔄 实时调整
- 无需重新运行Python代码
- 参数修改即时生效
- 快速对比不同策略效果

### 🎨 用户友好
- 可折叠面板，不影响主界面
- 清晰的参数说明和取值范围
- 直观的操作反馈

### 📊 专业功能
- 完整的参数验证机制
- 详细的参数说明
- 一键重置功能

这个动态参数调节功能让您可以像专业交易员一样，实时调整策略参数，快速找到最适合当前市场环境的交易策略！
