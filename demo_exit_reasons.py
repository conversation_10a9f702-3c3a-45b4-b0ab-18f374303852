#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
演示出场原因功能 - 使用实际股票数据
"""

import pandas as pd
from points import StockTrendCalculator, SmartTradingStrategy, get_data

def demo_exit_reasons():
    """演示不同的出场原因"""
    print("🚀 演示出场原因功能")
    print("="*50)
    
    # 获取股票数据
    print("📊 获取股票数据...")
    stock_data = get_data('003021', '20230101', '20240630')
    
    if not stock_data:
        print("❌ 无法获取股票数据")
        return
    
    print(f"✅ 成功获取 {len(stock_data)} 条数据")
    
    # 创建计算器和策略 - 使用更敏感的参数来触发更多交易
    calculator = StockTrendCalculator(window_size=30, turn_point_threshold=8.0)
    
    # 设置较敏感的止损止盈参数
    strategy = SmartTradingStrategy(
        buy_threshold=6.0,      # 较低的买入阈值
        sell_threshold=12.0,    # 较低的卖出阈值
        stop_loss=6.0,          # 6%固定止损（更敏感）
        take_profit=15.0,       # 15%止盈（更容易触发）
        trailing_stop=4.0       # 4%移动止损（更敏感）
    )
    
    calculator.trading_strategy = strategy
    
    print(f"\n📈 策略参数:")
    print(f"   买入阈值: {strategy.buy_threshold}%")
    print(f"   卖出阈值: {strategy.sell_threshold}%")
    print(f"   固定止损: {strategy.stop_loss}%")
    print(f"   止盈: {strategy.take_profit}%")
    print(f"   移动止损: {strategy.trailing_stop}%")
    
    # 逐日处理数据
    print(f"\n🔄 开始处理数据...")
    trade_count = 0

    for i, record in enumerate(stock_data):
        # record已经是字典格式，添加day_index
        record['day_index'] = i

        calculator.add_record(record['date'], record['high'], record['low'], record['close'])
        
        # 处理交易信号
        daily_signals = strategy.process_daily_data(calculator, record)
        
        if daily_signals:
            for signal in daily_signals:
                if signal.signal_type == 'SELL':
                    trade_count += 1
                    print(f"📅 第{i+1}天 ({record['date']}): {signal.signal_type} @ {signal.price:.2f} - {signal.reason}")
                elif signal.signal_type == 'BUY':
                    print(f"📅 第{i+1}天 ({record['date']}): {signal.signal_type} @ {signal.price:.2f} - {signal.reason}")
        
        # 限制输出，只显示前20个交易
        if trade_count >= 10:
            print("   ... (更多交易记录)")
            break
    
    # 显示交易结果
    print(f"\n📊 交易结果:")
    performance = strategy.calculate_performance()
    if performance:
        print(f"   总收益率: {performance['total_return']:.2f}%")
        print(f"   完成交易: {performance['total_trades']}笔")
        print(f"   胜率: {performance['win_rate']:.1f}%")
        print(f"   最大回撤: {performance['max_drawdown']:.2f}%")
    
    # 统计出场原因
    print(f"\n📋 出场原因统计:")
    exit_reasons = {}
    for trade in strategy.trade_history:
        if trade['type'] == 'SELL':
            reason = trade['reason']
            # 简化原因显示
            if '固定止损' in reason:
                simple_reason = '固定止损'
            elif '移动止损' in reason:
                simple_reason = '移动止损'
            elif '止盈' in reason:
                simple_reason = '止盈'
            else:
                simple_reason = '转折点信号'
            
            exit_reasons[simple_reason] = exit_reasons.get(simple_reason, 0) + 1
    
    for reason, count in exit_reasons.items():
        print(f"   {reason}: {count}次")
    
    # 显示详细的最近几笔交易
    print(f"\n📝 最近的交易记录:")
    sell_trades = [t for t in strategy.trade_history if t['type'] == 'SELL']
    for trade in sell_trades[-5:]:  # 显示最后5笔卖出交易
        print(f"   {trade['date']}: 卖出 {trade['quantity']}股@{trade['price']:.2f} - {trade['reason']}")
    
    print(f"\n✅ 演示完成！现在您可以在浏览器中查看完整的交易记录表格，")
    print(f"   其中包含了带颜色标识的出场原因列。")

if __name__ == "__main__":
    demo_exit_reasons()
