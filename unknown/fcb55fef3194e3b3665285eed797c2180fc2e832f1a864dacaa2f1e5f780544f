<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易信号调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .log { background-color: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>交易信号调试</h1>
        
        <div class="debug-section">
            <h2>测试步骤</h2>
            <button onclick="testTradingStrategy()">测试交易策略初始化</button>
            <button onclick="testSignalGeneration()">测试信号生成</button>
            <button onclick="testTradingExecution()">测试交易执行</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="debug-section">
            <h2>调试日志</h2>
            <div id="debugLog" class="log">点击上方按钮开始调试...</div>
        </div>
    </div>

    <script>
        // 简化的测试数据
        const testData = [
            {date: '2024-01-01', high: 100, low: 95, close: 98},
            {date: '2024-01-02', high: 102, low: 97, close: 101},
            {date: '2024-01-03', high: 105, low: 100, close: 103},
            {date: '2024-01-04', high: 108, low: 103, close: 106},
            {date: '2024-01-05', high: 110, low: 105, close: 108},
            {date: '2024-01-06', high: 112, low: 107, close: 109},
            {date: '2024-01-07', high: 115, low: 110, close: 113},
            {date: '2024-01-08', high: 118, low: 113, close: 116},
            {date: '2024-01-09', high: 120, low: 115, close: 118},
            {date: '2024-01-10', high: 125, low: 118, close: 122},
            // 开始下跌
            {date: '2024-01-11', high: 122, low: 115, close: 117},
            {date: '2024-01-12', high: 118, low: 110, close: 112},
            {date: '2024-01-13', high: 115, low: 105, close: 108},
            {date: '2024-01-14', high: 110, low: 100, close: 103},
            {date: '2024-01-15', high: 105, low: 95, close: 98},
            // 再次上涨
            {date: '2024-01-16', high: 102, low: 96, close: 100},
            {date: '2024-01-17', high: 105, low: 99, close: 103},
            {date: '2024-01-18', high: 108, low: 102, close: 106},
            {date: '2024-01-19', high: 112, low: 106, close: 110},
            {date: '2024-01-20', high: 115, low: 109, close: 113}
        ];

        let debugLog = document.getElementById('debugLog');
        let calculator = null;

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.textContent += `[${timestamp}] ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            debugLog.textContent = '';
        }

        // 复制JavaScript类定义（简化版）
        class EnhancedTurnPointDetector {
            constructor(percentChangeThreshold = 10.0) {
                this.threshold = percentChangeThreshold / 100.0;
                this.records = [];
                this.turn_points = [];
                this.last_extreme_price = null;
                this.last_extreme_point_info = null;
                this.current_trend = 0;
                this.temp_first_point_high_info = null;
                this.temp_first_point_low_info = null;
            }

            addRecord(date, high, low, dayIndex) {
                this.records.push({ date, high, low, dayIndex });

                if (this.current_trend === 0) {
                    if (!this.temp_first_point_high_info) {
                        this.temp_first_point_high_info = { date, price: high, dayIndex };
                        this.temp_first_point_low_info = { date, price: low, dayIndex };
                    } else {
                        const firstHigh = this.temp_first_point_high_info.price;

                        if (high > firstHigh * (1 + this.threshold)) {
                            this.current_trend = 1;
                            this.turn_points.push({
                                date: this.temp_first_point_low_info.date,
                                price: this.temp_first_point_low_info.price,
                                pointType: 'TROUGH',
                                dayIndex: this.temp_first_point_low_info.dayIndex
                            });
                            this.last_extreme_price = high;
                            this.last_extreme_point_info = { date, price: high, dayIndex };
                        } else if (low < firstHigh * (1 - this.threshold)) {
                            this.current_trend = -1;
                            this.turn_points.push({
                                date: this.temp_first_point_high_info.date,
                                price: this.temp_first_point_high_info.price,
                                pointType: 'PEAK',
                                dayIndex: this.temp_first_point_high_info.dayIndex
                            });
                            this.last_extreme_price = low;
                            this.last_extreme_point_info = { date, price: low, dayIndex };
                        } else {
                            if (high > this.temp_first_point_high_info.price) {
                                this.temp_first_point_high_info = { date, price: high, dayIndex };
                            }
                            if (low < this.temp_first_point_low_info.price) {
                                this.temp_first_point_low_info = { date, price: low, dayIndex };
                            }
                        }
                    }
                } else if (this.current_trend === 1) {
                    if (high > this.last_extreme_price) {
                        this.last_extreme_price = high;
                        this.last_extreme_point_info = { date, price: high, dayIndex };
                    } else if (low < this.last_extreme_price * (1 - this.threshold)) {
                        this.turn_points.push({
                            date: this.last_extreme_point_info.date,
                            price: this.last_extreme_point_info.price,
                            pointType: 'PEAK',
                            dayIndex: this.last_extreme_point_info.dayIndex
                        });
                        this.current_trend = -1;
                        this.last_extreme_price = low;
                        this.last_extreme_point_info = { date, price: low, dayIndex };
                    }
                } else if (this.current_trend === -1) {
                    if (low < this.last_extreme_price) {
                        this.last_extreme_price = low;
                        this.last_extreme_point_info = { date, price: low, dayIndex };
                    } else if (high > this.last_extreme_price * (1 + this.threshold)) {
                        this.turn_points.push({
                            date: this.last_extreme_point_info.date,
                            price: this.last_extreme_point_info.price,
                            pointType: 'TROUGH',
                            dayIndex: this.last_extreme_point_info.dayIndex
                        });
                        this.current_trend = 1;
                        this.last_extreme_price = high;
                        this.last_extreme_point_info = { date, price: high, dayIndex };
                    }
                }
            }

            getPeaks() { return this.turn_points.filter(p => p.pointType === 'PEAK'); }
            getTroughs() { return this.turn_points.filter(t => t.pointType === 'TROUGH'); }
        }

        class SmartTradingStrategy {
            constructor(buyThreshold = 8.0, sellThreshold = 15.0) {
                this.buyThreshold = buyThreshold / 100.0;
                this.sellThreshold = sellThreshold / 100.0;
                this.signals = [];
            }

            analyzeSignals(calculator) {
                const peaks = calculator.turnPointDetector.getPeaks();
                const troughs = calculator.turnPointDetector.getTroughs();

                log(`分析信号: 波峰${peaks.length}个, 波谷${troughs.length}个`);

                // 生成买入信号
                for (let trough of troughs) {
                    const existingSignal = this.signals.find(s =>
                        s.dayIndex === trough.dayIndex && s.signalType === 'BUY'
                    );

                    if (!existingSignal && this.isUptrendBuyOpportunity(trough, calculator)) {
                        this.signals.push({
                            date: trough.date,
                            price: trough.price,
                            signalType: 'BUY',
                            dayIndex: trough.dayIndex,
                            reason: '上升趋势波谷买入',
                            confidence: 0.8
                        });
                        log(`生成买入信号: ${trough.date} @ ${trough.price}`);
                    }
                }

                // 生成卖出信号
                for (let peak of peaks) {
                    const existingSignal = this.signals.find(s =>
                        s.dayIndex === peak.dayIndex && s.signalType === 'SELL'
                    );

                    if (!existingSignal && this.isProfitTakingOpportunity(peak, calculator)) {
                        this.signals.push({
                            date: peak.date,
                            price: peak.price,
                            signalType: 'SELL',
                            dayIndex: peak.dayIndex,
                            reason: '高位获利了结',
                            confidence: 0.8
                        });
                        log(`生成卖出信号: ${peak.date} @ ${peak.price}`);
                    }
                }

                return this.signals;
            }

            isUptrendBuyOpportunity(trough, calculator) {
                const troughs = calculator.turnPointDetector.getTroughs();
                const troughIndex = troughs.findIndex(t => t.dayIndex === trough.dayIndex);
                if (troughIndex > 0) {
                    return trough.price > troughs[troughIndex - 1].price;
                }
                return false;
            }

            isProfitTakingOpportunity(peak, calculator) {
                const peaks = calculator.turnPointDetector.getPeaks();
                const peakIndex = peaks.findIndex(p => p.dayIndex === peak.dayIndex);
                if (peakIndex > 0) {
                    return peak.price > peaks[peakIndex - 1].price;
                }
                return true;
            }

            getBuySignals() { return this.signals.filter(s => s.signalType === 'BUY'); }
            getSellSignals() { return this.signals.filter(s => s.signalType === 'SELL'); }
        }

        class StockTrendCalculator {
            constructor(windowSize, turnPointThreshold, enableTrading = true) {
                this.windowSize = windowSize;
                this.records = [];
                this.dayCounter = 0;
                this.turnPointDetector = new EnhancedTurnPointDetector(turnPointThreshold);
                this.tradingStrategy = enableTrading ? new SmartTradingStrategy(8.0, 15.0) : null;
                log(`初始化计算器: 窗口=${windowSize}, 阈值=${turnPointThreshold}%, 交易策略=${enableTrading ? '启用' : '禁用'}`);
            }

            addRecord(timestamp, high, low, close) {
                const record = { date: timestamp, high: high, low: low, close: close, dayIndex: this.dayCounter };
                this.records.push(record);
                this.turnPointDetector.addRecord(timestamp, high, low, this.dayCounter);
                this.dayCounter++;
                if (this.records.length > this.windowSize) {
                    this.records.shift();
                }
            }

            getTradingSignals() {
                if (!this.tradingStrategy) return [];
                return this.tradingStrategy.analyzeSignals(this);
            }

            getBuySignals() {
                if (!this.tradingStrategy) return [];
                return this.tradingStrategy.getBuySignals();
            }

            getSellSignals() {
                if (!this.tradingStrategy) return [];
                return this.tradingStrategy.getSellSignals();
            }
        }

        function testTradingStrategy() {
            log('=== 测试交易策略初始化 ===');
            calculator = new StockTrendCalculator(60, 10.0, true);
            
            if (calculator.tradingStrategy) {
                log('✅ 交易策略初始化成功');
                log(`买入阈值: ${calculator.tradingStrategy.buyThreshold * 100}%`);
                log(`卖出阈值: ${calculator.tradingStrategy.sellThreshold * 100}%`);
            } else {
                log('❌ 交易策略初始化失败');
            }
        }

        function testSignalGeneration() {
            if (!calculator) {
                log('❌ 请先初始化交易策略');
                return;
            }

            log('=== 测试信号生成 ===');
            
            // 添加测试数据
            testData.forEach((record, index) => {
                calculator.addRecord(record.date, record.high, record.low, record.close);
                log(`添加数据 ${index + 1}: ${record.date} H=${record.high} L=${record.low} C=${record.close}`);
            });

            // 分析信号
            const signals = calculator.getTradingSignals();
            const buySignals = calculator.getBuySignals();
            const sellSignals = calculator.getSellSignals();

            log(`总信号数: ${signals.length}`);
            log(`买入信号: ${buySignals.length}个`);
            log(`卖出信号: ${sellSignals.length}个`);

            buySignals.forEach(signal => {
                log(`🟢 买入: ${signal.date} @ ${signal.price} (${signal.reason})`);
            });

            sellSignals.forEach(signal => {
                log(`🔴 卖出: ${signal.date} @ ${signal.price} (${signal.reason})`);
            });
        }

        function testTradingExecution() {
            if (!calculator) {
                log('❌ 请先初始化交易策略');
                return;
            }

            log('=== 测试交易执行 ===');
            
            let cash = 100000;
            let position = null;
            let tradeCount = 0;

            const buySignals = calculator.getBuySignals();
            const sellSignals = calculator.getSellSignals();

            // 按日期排序所有信号
            const allSignals = [...buySignals, ...sellSignals].sort((a, b) => 
                new Date(a.date) - new Date(b.date)
            );

            allSignals.forEach(signal => {
                if (signal.signalType === 'BUY' && !position) {
                    const quantity = Math.floor(cash / signal.price);
                    const amount = quantity * signal.price;
                    if (quantity > 0) {
                        cash -= amount;
                        position = { quantity, entryPrice: signal.price, entryDate: signal.date, entryAmount: amount };
                        tradeCount++;
                        log(`🟢 执行买入: ${signal.date} @ ${signal.price}, 数量=${quantity}, 金额=${amount.toFixed(2)}, 剩余现金=${cash.toFixed(2)}`);
                    }
                } else if (signal.signalType === 'SELL' && position) {
                    const sellAmount = position.quantity * signal.price;
                    const profit = sellAmount - position.entryAmount;
                    const profitRate = (profit / position.entryAmount) * 100;
                    cash += sellAmount;
                    log(`🔴 执行卖出: ${signal.date} @ ${signal.price}, 数量=${position.quantity}, 金额=${sellAmount.toFixed(2)}, 盈亏=${profit.toFixed(2)} (${profitRate.toFixed(2)}%), 现金=${cash.toFixed(2)}`);
                    position = null;
                    tradeCount++;
                }
            });

            log(`交易统计: 完成${Math.floor(tradeCount/2)}笔交易, 最终现金=${cash.toFixed(2)}`);
            if (position) {
                log(`当前持仓: ${position.quantity}股 @ ${position.entryPrice}`);
            }
        }
    </script>
</body>
</html>
