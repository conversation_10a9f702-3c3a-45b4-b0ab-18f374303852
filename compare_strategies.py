#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比较新旧策略的差异
展示逐日分析与批量分析的区别
"""

import sys
import os
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from points import StockTrendCalculator

def load_sample_data():
    """
    加载示例数据 - 使用一个有明显趋势的数据集
    """
    # 模拟一个典型的股票走势：上涨->回调->再上涨->大跌->反弹
    base_prices = [
        100, 102, 105, 103, 107, 110, 108, 112, 115, 113,  # 上涨趋势
        116, 118, 115, 119, 122, 120, 125, 128, 126, 130,  # 继续上涨
        127, 124, 121, 118, 115, 112, 109, 106, 103, 100,  # 回调
        102, 105, 108, 111, 114, 117, 120, 123, 126, 129,  # 反弹上涨
        132, 135, 138, 141, 144, 147, 150, 153, 156, 159,  # 强势上涨
        156, 153, 150, 147, 144, 141, 138, 135, 132, 129,  # 开始下跌
        126, 123, 120, 117, 114, 111, 108, 105, 102, 99,   # 持续下跌
        96, 93, 90, 87, 84, 81, 78, 75, 72, 69,            # 大幅下跌
        72, 75, 78, 81, 84, 87, 90, 93, 96, 99,            # 反弹
        102, 105, 108, 111, 114, 117, 120, 123, 126, 129   # 恢复上涨
    ]
    
    data = []
    start_date = datetime(2024, 1, 1)
    
    for i, price in enumerate(base_prices):
        date = start_date + timedelta(days=i)
        # 添加一些随机波动
        high = price * 1.02
        low = price * 0.98
        close = price
        
        data.append({
            'date': date.strftime('%Y-%m-%d'),
            'high': high,
            'low': low,
            'close': close
        })
    
    return data

def test_daily_vs_batch_analysis():
    """
    比较逐日分析与批量分析的差异
    """
    print("=== 比较逐日分析与批量分析 ===\n")
    
    # 加载测试数据
    data = load_sample_data()
    print(f"测试数据: {len(data)} 天，价格范围: {min(d['close'] for d in data):.1f} - {max(d['close'] for d in data):.1f}")
    
    # 方法1: 逐日分析（新方法）
    print("\n--- 逐日分析方法 ---")
    calculator_daily = StockTrendCalculator(
        window_size=60,
        turn_point_threshold=8.0,
        use_advanced_detector=True,
        enable_trading=True
    )
    
    daily_signals_timeline = []
    
    for i, record in enumerate(data):
        calculator_daily.add_record(
            timestamp=record['date'],
            high=record['high'],
            low=record['low'],
            close=record['close']
        )
        
        # 记录每日的信号状态
        strategy = calculator_daily.trading_strategy
        daily_signals_timeline.append({
            'day': i + 1,
            'date': record['date'],
            'price': record['close'],
            'signals_count': len(strategy.daily_signals),
            'position': strategy.position.copy() if strategy.position else None,
            'portfolio_value': strategy.portfolio_value
        })
    
    daily_performance = calculator_daily.trading_strategy.calculate_performance()
    
    # 方法2: 批量分析（旧方法）
    print("\n--- 批量分析方法 ---")
    calculator_batch = StockTrendCalculator(
        window_size=60,
        turn_point_threshold=8.0,
        use_advanced_detector=True,
        enable_trading=True
    )
    
    # 先添加所有数据
    for record in data:
        calculator_batch.add_record(
            timestamp=record['date'],
            high=record['high'],
            low=record['low'],
            close=record['close']
        )
    
    # 然后一次性分析信号（模拟旧方法）
    batch_signals = calculator_batch.trading_strategy.analyze_signals(calculator_batch)
    
    # 比较结果
    print(f"\n=== 结果比较 ===")
    print(f"逐日分析:")
    print(f"  总信号数: {len(calculator_daily.trading_strategy.daily_signals)}")
    print(f"  买入信号: {len([s for s in calculator_daily.trading_strategy.daily_signals if s.signal_type == 'BUY'])}")
    print(f"  卖出信号: {len([s for s in calculator_daily.trading_strategy.daily_signals if s.signal_type == 'SELL'])}")
    print(f"  完成交易: {daily_performance['total_trades']}")
    print(f"  最终收益: {daily_performance['total_return']:.1f}%")
    print(f"  最终价值: {daily_performance['portfolio_value']:.2f}")
    
    print(f"\n批量分析:")
    print(f"  总信号数: {len(batch_signals)}")
    print(f"  买入信号: {len([s for s in batch_signals if s.signal_type == 'BUY'])}")
    print(f"  卖出信号: {len([s for s in batch_signals if s.signal_type == 'SELL'])}")
    
    # 显示逐日信号生成时间线
    print(f"\n=== 逐日信号生成时间线 ===")
    prev_signals = 0
    for timeline in daily_signals_timeline:
        if timeline['signals_count'] > prev_signals:
            new_signals = calculator_daily.trading_strategy.daily_signals[prev_signals:timeline['signals_count']]
            print(f"第{timeline['day']}天 ({timeline['date']}):")
            for signal in new_signals:
                print(f"  {signal.signal_type}: {signal.price:.2f} - {signal.reason}")
            print(f"  组合价值: {timeline['portfolio_value']:.2f}")
            if timeline['position']:
                print(f"  持仓: {timeline['position']['type']} {timeline['position']['quantity']}股")
            else:
                print(f"  持仓: 无")
            prev_signals = timeline['signals_count']
    
    # 显示关键差异
    print(f"\n=== 关键差异分析 ===")
    print("1. 信号生成时机:")
    print("   - 逐日分析: 每天实时生成信号，可以及时响应市场变化")
    print("   - 批量分析: 基于历史数据回测，可能包含未来信息")
    
    print("\n2. 头寸管理:")
    print("   - 逐日分析: 实时跟踪持仓状态，支持止损止盈")
    print("   - 批量分析: 无实时头寸管理，仅生成信号")
    
    print("\n3. 实用性:")
    print("   - 逐日分析: 适合实盘交易，符合实际决策流程")
    print("   - 批量分析: 适合历史回测，但不适合实时交易")
    
    return calculator_daily, calculator_batch

def demonstrate_real_time_features():
    """
    演示实时功能特性
    """
    print("\n=== 演示实时功能特性 ===")
    
    calculator = StockTrendCalculator(enable_trading=True)
    strategy = calculator.trading_strategy
    
    # 模拟实时数据流
    test_prices = [100, 102, 105, 103, 107, 110, 108, 112, 115, 113, 116, 118, 115, 119, 122]
    
    print("模拟实时数据流处理:")
    for i, price in enumerate(test_prices):
        date = f"2024-01-{i+1:02d}"
        
        # 添加新数据
        calculator.add_record(
            timestamp=date,
            high=price * 1.01,
            low=price * 0.99,
            close=price
        )
        
        # 检查策略状态变化
        current_signals = len(strategy.daily_signals)
        position_status = "有持仓" if strategy.position else "无持仓"
        
        print(f"  {date}: 价格={price:.1f}, 信号数={current_signals}, {position_status}, 价值={strategy.portfolio_value:.0f}")
        
        # 如果有新信号，显示详情
        if current_signals > 0:
            latest_signal = strategy.daily_signals[-1]
            if latest_signal.date == date:
                print(f"    -> 新信号: {latest_signal.signal_type} @ {latest_signal.price:.2f}")

if __name__ == "__main__":
    try:
        # 比较分析方法
        daily_calc, batch_calc = test_daily_vs_batch_analysis()
        
        # 演示实时功能
        demonstrate_real_time_features()
        
        print("\n=== 测试完成 ===")
        print("新的逐日分析策略已成功实现，支持:")
        print("✓ 实时信号生成")
        print("✓ 逐日头寸管理") 
        print("✓ 止损止盈控制")
        print("✓ 完整的交易记录")
        print("✓ 实时性能统计")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
