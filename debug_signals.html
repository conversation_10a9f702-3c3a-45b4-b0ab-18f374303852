
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>交易信号调试</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        #chart { width: 100%; height: 400px; }
        #debug { margin-top: 20px; padding: 10px; background: #f5f5f5; }
        .debug-section { margin: 10px 0; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>🔍 交易信号调试</h1>
    
    <div id="chart"></div>
    
    <div>
        <button onclick="addNextDay()">添加下一个交易日</button>
        <button onclick="showDebugInfo()">显示调试信息</button>
        <button onclick="testSignalGeneration()">测试信号生成</button>
    </div>
    
    <div id="debug">
        <div class="debug-section">
            <h3>📊 当前状态</h3>
            <div id="status">等待开始...</div>
        </div>
        
        <div class="debug-section">
            <h3>🎯 转折点信息</h3>
            <div id="turnpoints">无转折点</div>
        </div>
        
        <div class="debug-section">
            <h3>📈 交易信号</h3>
            <div id="signals">无信号</div>
        </div>
        
        <div class="debug-section">
            <h3>💰 交易记录</h3>
            <div id="trades">无交易记录</div>
        </div>
    </div>

    <script>
        // 股票数据
        const stockData = [{'date': '2024-01-01', 'open': 90.26, 'high': 90.8, 'low': 88.8, 'close': 89.8}, {'date': '2024-01-02', 'open': 101.77, 'high': 102.34, 'low': 100.34, 'close': 101.34}, {'date': '2024-01-03', 'open': 109.98, 'high': 110.7, 'low': 108.7, 'close': 109.7}, {'date': '2024-01-04', 'open': 98.52, 'high': 99.68, 'low': 97.68, 'close': 98.68}, {'date': '2024-01-05', 'open': 91.59, 'high': 92.77, 'low': 90.77, 'close': 91.77}, {'date': '2024-01-06', 'open': 97.72, 'high': 99.11, 'low': 97.11, 'close': 98.11}, {'date': '2024-01-07', 'open': 110.29, 'high': 110.97, 'low': 108.97, 'close': 109.97}, {'date': '2024-01-08', 'open': 99.59, 'high': 100.54, 'low': 98.54, 'close': 99.54}, {'date': '2024-01-09', 'open': 88.8, 'high': 90.13, 'low': 88.13, 'close': 89.13}, {'date': '2024-01-10', 'open': 99.14, 'high': 100.22, 'low': 98.22, 'close': 99.22}, {'date': '2024-01-11', 'open': 110.09, 'high': 110.66, 'low': 108.66, 'close': 109.66}, {'date': '2024-01-12', 'open': 99.12, 'high': 99.84, 'low': 97.84, 'close': 98.84}, {'date': '2024-01-13', 'open': 88.59, 'high': 90.06, 'low': 88.06, 'close': 89.06}, {'date': '2024-01-14', 'open': 100.59, 'high': 101.77, 'low': 99.77, 'close': 100.77}, {'date': '2024-01-15', 'open': 110.1, 'high': 110.69, 'low': 108.69, 'close': 109.69}, {'date': '2024-01-16', 'open': 98.18, 'high': 99.11, 'low': 97.11, 'close': 98.11}, {'date': '2024-01-17', 'open': 88.41, 'high': 89.14, 'low': 87.14, 'close': 88.14}, {'date': '2024-01-18', 'open': 100.68, 'high': 102.14, 'low': 100.14, 'close': 101.14}, {'date': '2024-01-19', 'open': 109.22, 'high': 110.1, 'low': 108.1, 'close': 109.1}, {'date': '2024-01-20', 'open': 100.1, 'high': 101.57, 'low': 99.57, 'close': 100.57}];
        
        // 转折点检测器
        class EnhancedTurnPointDetector {
            constructor(threshold) {
                this.threshold = threshold / 100.0;
                this.turn_points = [];
            }

            addRecord(date, high, low, close) {
                const record = { date, high, low, close, dayIndex: this.turn_points.length };
                
                // 简化的转折点检测：每3个点检测一次
                if (this.turn_points.length >= 2) {
                    const prev2 = this.turn_points[this.turn_points.length - 2];
                    const prev1 = this.turn_points[this.turn_points.length - 1];
                    
                    // 检测波峰：中间点高于两边
                    if (prev1.close > prev2.close && prev1.close > close) {
                        const change = Math.abs(prev1.close - prev2.close) / prev2.close;
                        if (change >= this.threshold) {
                            prev1.pointType = 'PEAK';
                            prev1.price = prev1.close;
                            console.log(`🔴 检测到波峰: 第${prev1.dayIndex}天, 价格: ${prev1.price}`);
                        }
                    }
                    
                    // 检测波谷：中间点低于两边
                    if (prev1.close < prev2.close && prev1.close < close) {
                        const change = Math.abs(prev2.close - prev1.close) / prev2.close;
                        if (change >= this.threshold) {
                            prev1.pointType = 'TROUGH';
                            prev1.price = prev1.close;
                            console.log(`🟢 检测到波谷: 第${prev1.dayIndex}天, 价格: ${prev1.price}`);
                        }
                    }
                }
                
                this.turn_points.push(record);
            }

            getPeaks() { 
                const peaks = this.turn_points.filter(p => p.pointType === 'PEAK');
                console.log(`📊 当前波峰数量: ${peaks.length}`);
                return peaks;
            }
            
            getTroughs() { 
                const troughs = this.turn_points.filter(t => t.pointType === 'TROUGH');
                console.log(`📊 当前波谷数量: ${troughs.length}`);
                return troughs;
            }
        }

        // 交易策略
        class SmartTradingStrategy {
            constructor(buyThreshold = 5.0, sellThreshold = 10.0) {
                this.buyThreshold = buyThreshold / 100.0;
                this.sellThreshold = sellThreshold / 100.0;
                this.signals = [];
                console.log(`📋 交易策略初始化: 买入阈值=${buyThreshold}%, 卖出阈值=${sellThreshold}%`);
            }

            analyzeSignals(calculator) {
                const peaks = calculator.turnPointDetector.getPeaks();
                const troughs = calculator.turnPointDetector.getTroughs();
                
                console.log(`🔍 分析信号: 波峰=${peaks.length}个, 波谷=${troughs.length}个`);

                // 生成买入信号 - 简化版：所有波谷都是买入信号
                for (let trough of troughs) {
                    const existingSignal = this.signals.find(s => 
                        s.dayIndex === trough.dayIndex && s.signalType === 'BUY'
                    );
                    
                    if (!existingSignal) {
                        this.signals.push({
                            date: trough.date,
                            price: trough.price,
                            signalType: 'BUY',
                            dayIndex: trough.dayIndex,
                            reason: '波谷买入信号',
                            confidence: 0.8
                        });
                        console.log(`✅ 生成买入信号: 第${trough.dayIndex}天, 价格: ${trough.price}`);
                    }
                }

                // 生成卖出信号 - 简化版：所有波峰都是卖出信号
                for (let peak of peaks) {
                    const existingSignal = this.signals.find(s => 
                        s.dayIndex === peak.dayIndex && s.signalType === 'SELL'
                    );
                    
                    if (!existingSignal) {
                        this.signals.push({
                            date: peak.date,
                            price: peak.price,
                            signalType: 'SELL',
                            dayIndex: peak.dayIndex,
                            reason: '波峰卖出信号',
                            confidence: 0.8
                        });
                        console.log(`✅ 生成卖出信号: 第${peak.dayIndex}天, 价格: ${peak.price}`);
                    }
                }

                console.log(`📊 总信号数: ${this.signals.length}个`);
                return this.signals;
            }

            getBuySignals() { 
                const buySignals = this.signals.filter(s => s.signalType === 'BUY');
                console.log(`🟢 买入信号: ${buySignals.length}个`);
                return buySignals;
            }
            
            getSellSignals() { 
                const sellSignals = this.signals.filter(s => s.signalType === 'SELL');
                console.log(`🔴 卖出信号: ${sellSignals.length}个`);
                return sellSignals;
            }
        }

        // 计算器
        class StockTrendCalculator {
            constructor(windowSize, turnPointThreshold) {
                this.windowSize = windowSize;
                this.records = [];
                this.dayCounter = 0;
                this.turnPointDetector = new EnhancedTurnPointDetector(turnPointThreshold);
                this.tradingStrategy = new SmartTradingStrategy(5.0, 10.0);
                console.log(`🚀 计算器初始化: 窗口=${windowSize}, 阈值=${turnPointThreshold}%`);
            }

            addRecord(date, high, low, close) {
                this.records.push({ date, high, low, close, dayIndex: this.dayCounter });
                this.turnPointDetector.addRecord(date, high, low, close);
                this.dayCounter++;
                
                // 分析信号
                this.tradingStrategy.analyzeSignals(this);
                
                console.log(`📅 添加记录: ${date}, 收盘价: ${close}, 总天数: ${this.dayCounter}`);
            }

            getBuySignals() { return this.tradingStrategy.getBuySignals(); }
            getSellSignals() { return this.tradingStrategy.getSellSignals(); }
        }

        // 初始化
        const calculator = new StockTrendCalculator(3, 5.0);
        let currentIndex = 0;
        let tradingRecords = [];
        let currentPosition = null;
        let cash = 100000;

        // 图表初始化
        const chart = echarts.init(document.getElementById('chart'));

        function updateChart() {
            const displayData = stockData.slice(0, currentIndex);
            
            const option = {
                title: { text: '股票价格调试图表' },
                xAxis: { 
                    type: 'category',
                    data: displayData.map(d => d.date)
                },
                yAxis: { type: 'value' },
                series: [{
                    name: '收盘价',
                    type: 'line',
                    data: displayData.map(d => d.close)
                }]
            };
            
            chart.setOption(option);
        }

        function addNextDay() {
            if (currentIndex >= stockData.length) {
                document.getElementById('status').textContent = '所有数据已添加完毕！';
                return;
            }
            
            const record = stockData[currentIndex];
            calculator.addRecord(record.date, record.high, record.low, record.close);
            currentIndex++;
            
            updateChart();
            updateDebugInfo();
            
            console.log(`🎯 当前进度: ${currentIndex}/${stockData.length}`);
        }

        function updateDebugInfo() {
            const peaks = calculator.turnPointDetector.getPeaks();
            const troughs = calculator.turnPointDetector.getTroughs();
            const buySignals = calculator.getBuySignals();
            const sellSignals = calculator.getSellSignals();
            
            document.getElementById('status').innerHTML = `
                当前天数: ${currentIndex}<br>
                现金: ${cash.toFixed(2)}<br>
                持仓: ${currentPosition ? '有' : '无'}
            `;
            
            document.getElementById('turnpoints').innerHTML = `
                波峰: ${peaks.length}个<br>
                波谷: ${troughs.length}个<br>
                详情: ${peaks.map(p => `第${p.dayIndex}天(${p.price})`).join(', ')}
            `;
            
            document.getElementById('signals').innerHTML = `
                买入信号: ${buySignals.length}个<br>
                卖出信号: ${sellSignals.length}个<br>
                买入详情: ${buySignals.map(s => `第${s.dayIndex}天(${s.price})`).join(', ')}<br>
                卖出详情: ${sellSignals.map(s => `第${s.dayIndex}天(${s.price})`).join(', ')}
            `;
            
            document.getElementById('trades').innerHTML = `
                交易记录: ${tradingRecords.length}条<br>
                详情: ${tradingRecords.map(t => `${t.date} ${t.type} ${t.price}`).join('<br>')}
            `;
        }

        function showDebugInfo() {
            console.log('=== 调试信息 ===');
            console.log('当前索引:', currentIndex);
            console.log('转折点:', calculator.turnPointDetector.turn_points);
            console.log('买入信号:', calculator.getBuySignals());
            console.log('卖出信号:', calculator.getSellSignals());
            console.log('交易记录:', tradingRecords);
        }

        function testSignalGeneration() {
            console.log('=== 测试信号生成 ===');
            
            // 手动添加几天数据
            for (let i = 0; i < Math.min(10, stockData.length); i++) {
                addNextDay();
            }
            
            showDebugInfo();
        }

        // 初始化图表
        updateChart();
        updateDebugInfo();
        
        console.log('🎉 调试页面加载完成！');
    </script>
</body>
</html>
    