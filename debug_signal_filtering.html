<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>信号过滤调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .log { background-color: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; }
        .highlight { background-color: yellow; }
    </style>
</head>
<body>
    <div class="container">
        <h1>信号过滤调试</h1>
        
        <div class="debug-section">
            <h2>测试步骤</h2>
            <button onclick="testSignalFiltering()">测试信号过滤</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="debug-section">
            <h2>调试日志</h2>
            <div id="debugLog" class="log">点击上方按钮开始调试...</div>
        </div>
    </div>

    <script>
        let debugLog = document.getElementById('debugLog');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.textContent += `[${timestamp}] ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            debugLog.textContent = '';
        }

        function testSignalFiltering() {
            log('=== 测试信号过滤逻辑 ===');
            
            // 模拟信号数据
            const allBuySignals = [
                { date: '2024-01-05', price: 108, dayIndex: 4 },
                { date: '2024-01-10', price: 122, dayIndex: 9 },
                { date: '2024-01-15', price: 98, dayIndex: 14 }
            ];
            
            const allSellSignals = [
                { date: '2024-01-08', price: 116, dayIndex: 7 },
                { date: '2024-01-12', price: 112, dayIndex: 11 },
                { date: '2024-01-18', price: 106, dayIndex: 17 }
            ];
            
            // 测试不同的当前日期
            const testDates = ['2024-01-05', '2024-01-08', '2024-01-10', '2024-01-12', '2024-01-15', '2024-01-18'];
            
            testDates.forEach(currentDate => {
                log(`\n--- 测试日期: ${currentDate} ---`);
                
                // 过滤当前日期的买入信号
                const todayBuySignals = allBuySignals.filter(signal => {
                    const match = signal.date === currentDate;
                    log(`买入信号 ${signal.date} === ${currentDate} ? ${match}`);
                    return match;
                });
                
                // 过滤当前日期的卖出信号
                const todaySellSignals = allSellSignals.filter(signal => {
                    const match = signal.date === currentDate;
                    log(`卖出信号 ${signal.date} === ${currentDate} ? ${match}`);
                    return match;
                });
                
                log(`结果: 买入信号 ${todayBuySignals.length}个, 卖出信号 ${todaySellSignals.length}个`);
                
                if (todayBuySignals.length > 0) {
                    log(`🟢 找到买入信号: ${JSON.stringify(todayBuySignals[0])}`);
                }
                
                if (todaySellSignals.length > 0) {
                    log(`🔴 找到卖出信号: ${JSON.stringify(todaySellSignals[0])}`);
                }
            });
            
            log('\n=== 测试完成 ===');
        }
    </script>
</body>
</html>
