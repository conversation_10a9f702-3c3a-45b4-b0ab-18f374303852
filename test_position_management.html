<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>持仓管理策略测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-result { background-color: #f9f9f9; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        #log { height: 300px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; border: 1px solid #ddd; font-family: monospace; font-size: 12px; }
        .log-entry { margin: 2px 0; }
        .log-buy { color: green; }
        .log-sell { color: red; }
        .log-ignore { color: orange; }
    </style>
</head>
<body>
    <div class="container">
        <h1>持仓管理策略测试</h1>
        
        <div class="test-section">
            <h2>测试说明</h2>
            <p>测试修改后的持仓管理策略：</p>
            <ul>
                <li>✅ 买入后全仓（使用所有现金）</li>
                <li>✅ 有持仓时忽略新的买入信号</li>
                <li>✅ 卖出后全仓卖出（卖出所有持仓）</li>
                <li>✅ 无持仓时忽略卖出信号</li>
                <li>✅ 严格按日期顺序处理</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>模拟测试</h2>
            <button onclick="runPositionManagementTest()">运行持仓管理测试</button>
            <button onclick="clearLog()">清空日志</button>
            
            <div id="testResults" class="test-result"></div>
            <div id="log"></div>
        </div>
    </div>

    <script>
        // 模拟交易记录和持仓状态
        let tradingRecords = [];
        let currentPosition = null;
        let cash = 100000;
        let tradeSequence = 0;
        let tradePairId = 0;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function calculateHoldDays(buyDate, sellDate) {
            const buy = new Date(buyDate);
            const sell = new Date(sellDate);
            return Math.ceil((sell - buy) / (1000 * 60 * 60 * 24));
        }

        function processTradeSignal(signal, currentDate) {
            log(`🔍 处理${signal.signalType}信号: ${currentDate} @ ${signal.price.toFixed(2)}`);

            if (signal.signalType === 'BUY') {
                // 持仓管理策略：只在无持仓时处理买入信号
                if (currentPosition) {
                    log(`❌ 已有持仓，忽略买入信号 - 当前持仓: ${currentPosition.quantity}股@${currentPosition.entryPrice.toFixed(2)}`, 'ignore');
                    return false;
                }

                // 检查是否已有当日买入记录
                if (tradingRecords.find(r => r.date === currentDate && r.type === 'BUY')) {
                    log(`❌ 当日已有买入记录，跳过`, 'ignore');
                    return false;
                }

                log(`✅ 当前无持仓，可以执行全仓买入`, 'buy');

                // 全仓买入：使用所有可用现金
                const quantity = Math.floor(cash / signal.price);
                const amount = quantity * signal.price;

                if (quantity > 0) {
                    tradePairId++;
                    currentPosition = {
                        type: 'LONG',
                        quantity: quantity,
                        entryPrice: signal.price,
                        entryDate: currentDate,
                        entryAmount: amount,
                        pairId: tradePairId
                    };

                    cash -= amount;
                    tradeSequence++;

                    const buyRecord = {
                        sequence: tradeSequence,
                        pairId: tradePairId,
                        date: currentDate,
                        type: 'BUY',
                        price: signal.price,
                        quantity: quantity,
                        amount: amount,
                        profit: 0,
                        profitRate: 0,
                        reason: '全仓买入信号',
                        pairInfo: null,
                        status: 'OPEN'
                    };

                    tradingRecords.push(buyRecord);
                    log(`🎉 全仓买入成功！数量: ${quantity}, 金额: ${amount.toFixed(2)}, 剩余现金: ${cash.toFixed(2)}`, 'buy');
                    return true;
                } else {
                    log(`❌ 买入失败：现金不足`, 'error');
                    return false;
                }

            } else if (signal.signalType === 'SELL') {
                // 持仓管理策略：只在有持仓时处理卖出信号
                if (!currentPosition) {
                    log(`❌ 当前无持仓，忽略卖出信号`, 'ignore');
                    return false;
                }

                // 检查是否已有当日卖出记录
                if (tradingRecords.find(r => r.date === currentDate && r.type === 'SELL')) {
                    log(`❌ 当日已有卖出记录，跳过`, 'ignore');
                    return false;
                }

                log(`✅ 当前有持仓，可以执行全仓卖出 - 持仓: ${currentPosition.quantity}股@${currentPosition.entryPrice.toFixed(2)}`, 'sell');

                // 全仓卖出：卖出所有持仓
                const sellQuantity = currentPosition.quantity;
                const sellAmount = sellQuantity * signal.price;
                const profit = sellAmount - currentPosition.entryAmount;
                const profitRate = (profit / currentPosition.entryAmount) * 100;

                cash += sellAmount;
                tradeSequence++;

                // 找到对应的买入记录并更新状态
                const buyRecord = tradingRecords.find(r => r.pairId === currentPosition.pairId && r.type === 'BUY');
                if (buyRecord) {
                    buyRecord.status = 'CLOSED';
                    buyRecord.pairInfo = {
                        sellDate: currentDate,
                        sellPrice: signal.price,
                        profit: profit,
                        profitRate: profitRate
                    };
                    buyRecord.profit = profit;
                    buyRecord.profitRate = profitRate;
                }

                tradingRecords.push({
                    sequence: tradeSequence,
                    pairId: currentPosition.pairId,
                    date: currentDate,
                    type: 'SELL',
                    price: signal.price,
                    quantity: sellQuantity,
                    amount: sellAmount,
                    profit: profit,
                    profitRate: profitRate,
                    reason: '全仓卖出信号',
                    pairInfo: {
                        buyDate: currentPosition.entryDate,
                        buyPrice: currentPosition.entryPrice,
                        holdDays: calculateHoldDays(currentPosition.entryDate, currentDate)
                    },
                    status: 'CLOSED'
                });

                currentPosition = null;
                log(`🎉 全仓卖出成功！现金: ${cash.toFixed(2)}, 盈亏: ${profit > 0 ? '+' : ''}${profit.toFixed(2)} (${profitRate > 0 ? '+' : ''}${profitRate.toFixed(2)}%)`, 'sell');
                return true;
            }

            return false;
        }

        function runPositionManagementTest() {
            // 重置状态
            tradingRecords = [];
            currentPosition = null;
            cash = 100000;
            tradeSequence = 0;
            tradePairId = 0;
            clearLog();

            log('=== 开始持仓管理策略测试 ===');

            // 模拟交易信号序列
            const testSignals = [
                { date: '2024-01-01', signalType: 'BUY', price: 100.0 },
                { date: '2024-01-02', signalType: 'BUY', price: 102.0 }, // 应该被忽略
                { date: '2024-01-03', signalType: 'SELL', price: 105.0 },
                { date: '2024-01-04', signalType: 'SELL', price: 103.0 }, // 应该被忽略
                { date: '2024-01-05', signalType: 'BUY', price: 98.0 },
                { date: '2024-01-06', signalType: 'BUY', price: 95.0 }, // 应该被忽略
                { date: '2024-01-07', signalType: 'SELL', price: 110.0 }
            ];

            // 按日期顺序处理信号
            testSignals.forEach(signal => {
                processTradeSignal(signal, signal.date);
            });

            // 显示测试结果
            const resultsDiv = document.getElementById('testResults');
            const completedTrades = tradingRecords.filter(r => r.type === 'SELL').length;
            const totalProfit = tradingRecords.filter(r => r.type === 'SELL').reduce((sum, r) => sum + r.profit, 0);
            
            resultsDiv.innerHTML = `
                <h3>测试结果</h3>
                <p><strong>总信号数:</strong> ${testSignals.length}</p>
                <p><strong>处理的交易:</strong> ${tradingRecords.length} 条记录</p>
                <p><strong>完成的交易对:</strong> ${completedTrades} 笔</p>
                <p><strong>最终现金:</strong> ${cash.toFixed(2)}</p>
                <p><strong>总盈亏:</strong> ${totalProfit > 0 ? '+' : ''}${totalProfit.toFixed(2)}</p>
                <p><strong>当前持仓:</strong> ${currentPosition ? `${currentPosition.quantity}股@${currentPosition.entryPrice.toFixed(2)}` : '无'}</p>
            `;

            log('=== 测试完成 ===');
            log(`预期结果: 应该有2笔买入、2笔卖出，忽略2个买入信号和1个卖出信号`);
            log(`实际结果: ${tradingRecords.filter(r => r.type === 'BUY').length}笔买入、${tradingRecords.filter(r => r.type === 'SELL').length}笔卖出`);
        }
    </script>
</body>
</html>
