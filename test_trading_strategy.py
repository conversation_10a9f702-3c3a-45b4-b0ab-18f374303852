#!/usr/bin/env python3
"""
测试修改后的持仓管理策略
"""

import points
import random

def test_trading_strategy():
    """测试交易策略的持仓管理"""
    print("=== 测试持仓管理策略 ===")
    
    # 生成测试数据
    test_data = []
    base_price = 100
    for i in range(30):
        # 模拟价格波动
        change = random.uniform(-0.08, 0.08)
        base_price *= (1 + change)
        high = base_price * random.uniform(1.001, 1.02)
        low = base_price * random.uniform(0.98, 0.999)
        close = base_price
        
        test_data.append({
            'date': f'2024-01-{i+1:02d}',
            'high': high,
            'low': low,
            'close': close
        })

    # 创建计算器并添加数据
    calculator = points.StockTrendCalculator(
        window_size=10, 
        turn_point_threshold=5.0, 
        enable_trading=True
    )
    
    for record in test_data:
        calculator.add_record(record['date'], record['high'], record['low'], record['close'])

    # 获取交易信号和策略表现
    buy_signals = calculator.get_buy_signals()
    sell_signals = calculator.get_sell_signals()
    performance = calculator.get_strategy_performance()

    print(f'买入信号数量: {len(buy_signals)}')
    print(f'卖出信号数量: {len(sell_signals)}')
    
    if buy_signals:
        print("\n买入信号详情:")
        for i, signal in enumerate(buy_signals, 1):
            print(f"  {i}. 日期: {signal.date}, 价格: {signal.price:.2f}, 原因: {signal.reason}")
    
    if sell_signals:
        print("\n卖出信号详情:")
        for i, signal in enumerate(sell_signals, 1):
            print(f"  {i}. 日期: {signal.date}, 价格: {signal.price:.2f}, 原因: {signal.reason}")
    
    if performance:
        print(f"\n策略表现:")
        print(f'  总交易次数: {performance["total_trades"]}')
        print(f'  总收益率: {performance["total_return"]*100:.2f}%')
        print(f'  最终资金: {performance["cash"]:.2f}')
        print(f'  组合价值: {performance["portfolio_value"]:.2f}')
        print(f'  胜率: {performance["win_rate"]:.1f}%')
        print(f'  最大回撤: {performance["max_drawdown"]:.2f}%')
        
        if performance["trades"]:
            print(f"\n交易记录:")
            for i, trade in enumerate(performance["trades"], 1):
                if trade.get("profit_ratio"):
                    profit_text = f"{trade['profit_ratio']*100:+.1f}%"
                    print(f"  {i}. {trade['date']} 卖出 @ {trade['price']:.2f} ({profit_text}) - {trade['reason']}")
    else:
        print("无策略表现数据")

if __name__ == "__main__":
    test_trading_strategy()
