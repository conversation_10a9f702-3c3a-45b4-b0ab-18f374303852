#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单调试：直接在processTradeSignals函数中添加console.log
"""

from points import create_interactive_chart

def create_simple_test():
    """创建简单测试数据"""
    data = []
    
    # 创建明显的价格变化：下降->上升->下降
    prices = [100, 95, 90, 85, 80, 85, 90, 95, 100, 105, 100, 95, 90]
    
    for i, price in enumerate(prices):
        date = f"2024-01-{i+1:02d}"
        data.append({
            'date': date,
            'open': price + 1,
            'high': price + 2,
            'low': price - 2,
            'close': price
        })
    
    return data

def main():
    """主函数"""
    print("🔍 简单调试测试")
    print("=" * 30)
    
    test_data = create_simple_test()
    
    print("📊 测试数据:")
    for record in test_data:
        print(f"  {record['date']}: {record['close']}")
    
    # 生成图表，使用很低的阈值确保能产生信号
    create_interactive_chart(
        data=test_data,
        window_size=3,  # 很小的窗口
        turn_point_threshold=3.0,  # 很低的阈值
        enable_trading=True
    )
    
    print("\n✅ 图表已生成: interactive_stock_analysis.html")
    print("🔍 请打开文件，按F12查看控制台，然后点击'添加下一个交易日'")

if __name__ == "__main__":
    main()
