#!/usr/bin/env python3
"""
修复版增强转折点策略

解决问题：
1. 同一天多笔交易 - 添加交易冷却期
2. 买卖不匹配 - 修复持仓状态管理
3. 价格异常 - 使用当前市场价格而非转折点价格
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging

from .base_strategy import BaseStrategy
from ..core.event import MarketEvent, SignalEvent, TurnPointEvent
from ..analysis.turn_point_detector import TurnPointDetector, VolumeSignal, SupportResistanceLevel


class FixedEnhancedTurnPointStrategy(BaseStrategy):
    """
    修复版增强转折点策略
    
    修复的问题：
    1. 交易频率控制 - 添加最小交易间隔
    2. 持仓状态管理 - 确保买卖匹配
    3. 价格合理性 - 使用市场价格而非信号价格
    4. 信号过滤 - 更严格的信号验证
    """
    
    def __init__(self, symbol: str):
        super().__init__("FixedEnhancedTurnPointStrategy", symbol)
        
        # 策略参数
        self.set_parameter('position_size', 100)
        self.set_parameter('min_volume_confirm_strength', 0.6)
        self.set_parameter('min_breakout_strength', 0.8)
        self.set_parameter('stop_loss_percent', 0.05)
        self.set_parameter('take_profit_percent', 0.10)
        self.set_parameter('max_position_hold_days', 15)
        self.set_parameter('min_trade_interval_days', 1)  # 最小交易间隔
        self.set_parameter('max_position_size', 1000)     # 最大持仓限制
        
        # 创建转折点检测器
        self.detector = TurnPointDetector(
            min_keep_days=3,
            min_extent_percent=3.0,  # 提高最小变化幅度要求
            volume_window=15,
            volume_threshold=1.5,
            sr_sensitivity=0.02
        )
        
        # 策略状态
        self.current_position = 0      # 当前持仓 (正数=多头, 负数=空头)
        self.entry_price = 0.0         # 入场价格
        self.entry_time = None         # 入场时间
        self.last_trade_time = None    # 最后交易时间
        self.last_turn_point = None    # 最后一个转折点
        self.processed_turn_points = set()  # 已处理的转折点集合
        
        # 信号统计
        self.signal_stats = {
            'total_signals': 0,
            'buy_signals': 0,
            'sell_signals': 0,
            'volume_confirmed': 0,
            'sr_confirmed': 0,
            'filtered_out': 0,
            'cooldown_filtered': 0
        }

        # 重新设置事件处理器映射，确保使用子类重写的方法
        self.event_handlers[TurnPointEvent] = self.on_turn_point_event
        self.logger.info(f"Event handler for TurnPointEvent set to: {self.event_handlers[TurnPointEvent]}")
    
    def on_market_event(self, event: MarketEvent) -> List[SignalEvent]:
        """
        处理市场数据事件

        Args:
            event: 市场数据事件

        Returns:
            List[SignalEvent]: 生成的交易信号列表
        """
        signals = []

        # 更新转折点检测器（但不使用其生成的转折点，只用于市场分析）
        # 注意：我们禁用内部转折点生成，只使用外部转折点事件
        self.detector.process_market_event(event)

        # 检查止损止盈
        stop_signals = self._check_stop_conditions(event)
        signals.extend(stop_signals)

        # 注意：我们不再处理内部转折点检测器生成的转折点
        # 所有转折点信号都通过外部转折点事件处理

        # 检查持仓时间限制
        time_signals = self._check_time_limits(event)
        signals.extend(time_signals)

        return signals

    def on_turn_point_event(self, event: TurnPointEvent) -> List[SignalEvent]:
        """
        处理转折点事件（来自外部检测器）

        Args:
            event: 转折点事件

        Returns:
            List[SignalEvent]: 生成的交易信号列表
        """
        self.logger.info(f"CHILD CLASS on_turn_point_event called: {event.point_type} at {event.price:.2f}")
        signals = []

        # 创建转折点唯一标识
        turn_point_id = f"{event.timestamp}_{event.price}_{event.point_type}"

        # 检查是否已经处理过这个转折点
        self.logger.info(f"Turn point ID: {turn_point_id}, already processed: {turn_point_id in self.processed_turn_points}")

        # 恢复去重逻辑，避免重复处理相同的转折点
        if turn_point_id not in self.processed_turn_points:
            self.processed_turn_points.add(turn_point_id)
            self.last_turn_point = event
            self.logger.info(f"Processing new turn point: {turn_point_id}")

            # 检查交易冷却期
            cooldown_ok = self._check_trade_cooldown(event.timestamp)
            self.logger.info(f"Cooldown check: {cooldown_ok}, last_trade_time: {self.last_trade_time}")
            if not cooldown_ok:
                self.signal_stats['cooldown_filtered'] += 1
                self.logger.info(f"Signal filtered due to cooldown period")
                return signals
        else:
            self.logger.info(f"Skipping already processed turn point: {turn_point_id}")
            return signals

        # 获取当前市场分析（使用内部检测器的分析）
        analysis = self.detector.get_market_analysis()
        volume_signals = self.detector.get_current_volume_signals()
        sr_levels = self.detector.get_support_resistance_levels()

        # 信号过滤和确认
        signal_strength = self._calculate_signal_strength(event, volume_signals, sr_levels, analysis)

        # 添加调试信息
        self.logger.info(f"External turn point signal strength: {signal_strength:.3f} for {event.point_type} at {event.price:.2f}")
        self.logger.info(f"  - Extent: {event.extent_percent:.3f}, Keep days: {event.keep_days}")
        self.logger.info(f"  - Volume signals: {len(volume_signals)}, SR levels: {len(sr_levels)}")

        if signal_strength < 0.3:  # 进一步降低信号强度要求
            self.signal_stats['filtered_out'] += 1
            self.logger.info(f"External signal filtered out: strength {signal_strength:.3f} too low (threshold: 0.3)")
            return signals

        position_size = self.get_parameter('position_size')

        # 生成交易信号 - 使用转折点价格
        current_price = event.price

        if event.point_type == 'TROUGH' and self.current_position == 0:
            # 谷底转折点 - 买入信号（仅在无持仓时）
            signal = self._create_buy_signal(current_price, event.timestamp, signal_strength,
                                           position_size, volume_signals, sr_levels)
            if signal:
                signals.append(signal)
                self.signal_stats['buy_signals'] += 1
                self.logger.info(f"Generated BUY signal at TROUGH: position={self.current_position}")

        elif event.point_type == 'PEAK' and self.current_position > 0:
            # 峰值转折点 - 卖出信号（仅在有持仓时）
            signal = self._create_sell_signal(current_price, event.timestamp, signal_strength,
                                            position_size, volume_signals, sr_levels)
            if signal:
                signals.append(signal)
                self.signal_stats['sell_signals'] += 1
                self.logger.info(f"Generated SELL signal at PEAK: position={self.current_position}")
        else:
            # 记录被忽略的信号
            if event.point_type == 'TROUGH' and self.current_position > 0:
                self.logger.info(f"Ignored TROUGH signal - already have position: {self.current_position}")
            elif event.point_type == 'PEAK' and self.current_position == 0:
                self.logger.info(f"Ignored PEAK signal - no position to sell: {self.current_position}")

        self.signal_stats['total_signals'] += len(signals)

        return signals

    def _process_turn_point(self, turn_point: TurnPointEvent, market_event: MarketEvent) -> List[SignalEvent]:
        """
        处理转折点，生成交易信号

        Args:
            turn_point: 转折点事件
            market_event: 市场数据事件

        Returns:
            List[SignalEvent]: 交易信号列表
        """
        signals = []

        # 重要修复：只在转折点发生的当天或之后的第一个交易日生成信号
        # 而不是在转折点被确认的日期生成信号
        turn_point_date = turn_point.timestamp.date()
        current_date = market_event.timestamp.date()

        # 如果当前日期早于转折点日期，不生成信号（这种情况不应该发生）
        if current_date < turn_point_date:
            self.logger.debug(f"Current date {current_date} is before turn point date {turn_point_date}, skipping")
            return signals

        # 如果转折点是历史转折点（不是当天的），不生成信号
        # 只处理当天或最近几天的转折点
        days_diff = (current_date - turn_point_date).days
        if days_diff > 3:  # 超过3天的历史转折点不处理
            self.logger.debug(f"Turn point is {days_diff} days old, skipping historical turn point")
            return signals

        # 检查交易冷却期（使用转折点的时间而不是当前时间）
        if not self._check_trade_cooldown(turn_point.timestamp):
            self.signal_stats['cooldown_filtered'] += 1
            self.logger.debug(f"Signal filtered due to cooldown period")
            return signals
        
        # 获取当前市场分析
        analysis = self.detector.get_market_analysis()
        volume_signals = self.detector.get_current_volume_signals()
        sr_levels = self.detector.get_support_resistance_levels()
        
        # 信号过滤和确认
        signal_strength = self._calculate_signal_strength(turn_point, volume_signals, sr_levels, analysis)

        # 添加调试信息
        self.logger.info(f"Turn point signal strength: {signal_strength:.3f} for {turn_point.point_type} at {turn_point.price:.2f}")
        self.logger.info(f"  - Extent: {turn_point.extent_percent:.3f}, Keep days: {turn_point.keep_days}")
        self.logger.info(f"  - Volume signals: {len(volume_signals)}, SR levels: {len(sr_levels)}")

        if signal_strength < 0.3:  # 进一步降低信号强度要求
            self.signal_stats['filtered_out'] += 1
            self.logger.info(f"Signal filtered out: strength {signal_strength:.3f} too low (threshold: 0.3)")
            return signals
        
        position_size = self.get_parameter('position_size')
        
        # 生成交易信号 - 使用当前市场价格而非转折点价格
        current_price = market_event.close
        
        if turn_point.point_type == 'TROUGH' and self.current_position == 0:
            # 谷底转折点 - 买入信号（仅在无持仓时）
            signal = self._create_buy_signal(current_price, market_event.timestamp, signal_strength,
                                           position_size, volume_signals, sr_levels)
            if signal:
                signals.append(signal)
                self.signal_stats['buy_signals'] += 1
                self.logger.info(f"Generated BUY signal at TROUGH: position={self.current_position}")

        elif turn_point.point_type == 'PEAK' and self.current_position > 0:
            # 峰值转折点 - 卖出信号（仅在有持仓时）
            signal = self._create_sell_signal(current_price, market_event.timestamp, signal_strength,
                                            position_size, volume_signals, sr_levels)
            if signal:
                signals.append(signal)
                self.signal_stats['sell_signals'] += 1
                self.logger.info(f"Generated SELL signal at PEAK: position={self.current_position}")
        else:
            # 记录被忽略的信号
            if turn_point.point_type == 'TROUGH' and self.current_position > 0:
                self.logger.info(f"Ignored TROUGH signal - already have position: {self.current_position}")
            elif turn_point.point_type == 'PEAK' and self.current_position == 0:
                self.logger.info(f"Ignored PEAK signal - no position to sell: {self.current_position}")
        
        self.signal_stats['total_signals'] += len(signals)
        return signals

    def on_fill_event(self, fill_event) -> None:
        """
        处理成交事件，更新策略状态

        Args:
            fill_event: 成交事件
        """
        # 更新持仓状态
        if fill_event.direction == 'BUY':
            self.current_position += fill_event.quantity
            self.entry_price = fill_event.fill_price
            self.entry_time = fill_event.timestamp
        else:  # SELL
            self.current_position -= fill_event.quantity
            if self.current_position <= 0:
                self.entry_price = 0.0
                self.entry_time = None

        # 更新最后交易时间
        self.last_trade_time = fill_event.timestamp

        self.logger.info(f"Position updated: {self.current_position} shares, "
                        f"entry_price: {self.entry_price}, last_trade: {self.last_trade_time}")

    def _check_trade_cooldown(self, current_time: datetime) -> bool:
        """
        检查交易冷却期

        Args:
            current_time: 当前时间

        Returns:
            bool: 是否可以交易
        """
        if self.last_trade_time is None:
            return True

        min_interval = self.get_parameter('min_trade_interval_days')
        time_diff = current_time - self.last_trade_time

        # 更严格的冷却期检查：必须是不同的日期
        current_date = current_time.date()
        last_trade_date = self.last_trade_time.date()

        # 如果是同一天，直接拒绝
        if current_date == last_trade_date:
            return False

        # 检查天数差
        return time_diff.days >= min_interval
    
    def _calculate_signal_strength(self, turn_point: TurnPointEvent,
                                 volume_signals: List[VolumeSignal],
                                 sr_levels: List[SupportResistanceLevel],
                                 analysis: Dict[str, Any]) -> float:
        """
        计算信号强度

        Args:
            turn_point: 转折点事件
            volume_signals: 量价信号列表
            sr_levels: 支撑阻力位列表
            analysis: 市场分析

        Returns:
            float: 信号强度 (0-1)
        """
        strength = 0.4  # 提高基础强度，确保大部分转折点都能通过

        # 1. 转折点本身的强度
        if abs(turn_point.extent_percent) > 0.02:  # 2%以上的变化就给分
            strength += 0.2
        if turn_point.keep_days >= 1:  # 持续1天以上就给分
            strength += 0.1
        
        # 2. 量价信号确认 - 降低要求
        volume_confirm_signals = [s for s in volume_signals if s.signal_type == 'VOLUME_CONFIRM']
        if volume_confirm_signals:
            max_volume_strength = max(s.strength for s in volume_confirm_signals)
            if max_volume_strength >= 0.3:  # 降低量价确认要求
                strength += 0.2
                self.signal_stats['volume_confirmed'] += 1

        # 3. 高成交量突破信号 - 降低要求
        breakout_signals = [s for s in volume_signals if s.signal_type == 'HIGH_VOLUME_BREAKOUT']
        if breakout_signals:
            max_breakout_strength = max(s.strength for s in breakout_signals)
            if max_breakout_strength >= 0.4:  # 降低突破信号要求
                strength += 0.2
        
        # 4. 支撑阻力位确认
        for level in sr_levels[:3]:  # 检查前3个最强的支撑阻力位
            price_diff = abs(turn_point.price - level.price) / level.price
            if price_diff <= 0.02:  # 在2%范围内
                strength += level.strength * 0.2
                self.signal_stats['sr_confirmed'] += 1
                break
        
        return min(strength, 1.0)  # 限制在1.0以内
    
    def _create_buy_signal(self, price: float, timestamp: datetime, strength: float, 
                          position_size: int, volume_signals: List[VolumeSignal],
                          sr_levels: List[SupportResistanceLevel]) -> Optional[SignalEvent]:
        """创建买入信号"""
        
        # 检查持仓限制
        if self.current_position >= self.get_parameter('max_position_size'):
            self.logger.debug("Cannot buy: position limit reached")
            return None
        
        # 构建信号原因
        reasons = [f"TROUGH转折点确认"]
        
        # 添加量价信号原因
        for signal in volume_signals:
            if signal.strength > 0.6:
                reasons.append(f"{signal.signal_type}({signal.strength:.2f})")
        
        reason = "; ".join(reasons)
        
        # 使用固定数量1，实际数量将在执行处理器中根据可用现金计算（全仓买入）
        signal = self._create_signal(
            signal_type='BUY',
            quantity=1,  # 占位符，执行处理器会重新计算为全仓数量
            price=None,  # 使用市价单而不是限价单
            reason=reason,
            confidence=strength
        )
        
        # 注意：不在这里更新持仓状态，等实际成交后再更新
        # 持仓状态将在 on_fill_event 中更新
        
        self.logger.info(f"BUY signal generated: {reason} @ {price:.2f} (strength: {strength:.2f})")
        return signal
    
    def _create_sell_signal(self, price: float, timestamp: datetime, strength: float,
                           position_size: int, volume_signals: List[VolumeSignal],
                           sr_levels: List[SupportResistanceLevel]) -> Optional[SignalEvent]:
        """创建卖出信号"""
        
        # 检查是否有持仓可卖
        if self.current_position <= 0:
            self.logger.debug("Cannot sell: no position")
            return None
        
        # 构建信号原因
        reasons = [f"PEAK转折点确认"]
        
        # 添加量价信号原因
        for signal in volume_signals:
            if signal.strength > 0.6:
                reasons.append(f"{signal.signal_type}({signal.strength:.2f})")
        
        reason = "; ".join(reasons)
        
        # 使用固定数量1，实际数量将在执行处理器中设置为全部持仓（全仓卖出）
        signal = self._create_signal(
            signal_type='SELL',
            quantity=1,  # 占位符，执行处理器会重新计算为全部持仓数量
            price=None,  # 使用市价单而不是限价单
            reason=reason,
            confidence=strength
        )
        
        # 注意：不在这里更新持仓状态，等实际成交后再更新
        # 持仓状态将在 on_fill_event 中更新
        
        self.logger.info(f"SELL signal generated: {reason} @ {price:.2f} (strength: {strength:.2f})")
        return signal

    def _check_stop_conditions(self, event: MarketEvent) -> List[SignalEvent]:
        """检查止损止盈条件"""
        signals = []

        if self.current_position == 0 or self.entry_price == 0:
            return signals

        current_price = event.close
        stop_loss_pct = self.get_parameter('stop_loss_percent')
        take_profit_pct = self.get_parameter('take_profit_percent')

        if self.current_position > 0:  # 多头持仓
            # 止损检查
            if current_price <= self.entry_price * (1 - stop_loss_pct):
                signal = self._create_signal(
                    signal_type='SELL',
                    quantity=self.current_position,
                    price=None,  # 使用市价单
                    reason=f'止损 (入场: {self.entry_price:.2f}, 当前: {current_price:.2f})',
                    confidence=1.0
                )
                signals.append(signal)
                self._reset_position()
                self.last_trade_time = event.timestamp

            # 止盈检查
            elif current_price >= self.entry_price * (1 + take_profit_pct):
                signal = self._create_signal(
                    signal_type='SELL',
                    quantity=self.current_position,
                    price=None,  # 使用市价单
                    reason=f'止盈 (入场: {self.entry_price:.2f}, 当前: {current_price:.2f})',
                    confidence=1.0
                )
                signals.append(signal)
                self._reset_position()
                self.last_trade_time = event.timestamp

        return signals

    def _check_time_limits(self, event: MarketEvent) -> List[SignalEvent]:
        """检查持仓时间限制"""
        signals = []

        if self.current_position == 0 or self.entry_time is None:
            return signals

        max_hold_days = self.get_parameter('max_position_hold_days')
        hold_duration = event.timestamp - self.entry_time

        if hold_duration.days >= max_hold_days:
            # 超过最大持仓时间，强制平仓
            if self.current_position > 0:
                signal = self._create_signal(
                    signal_type='SELL',
                    quantity=self.current_position,
                    price=None,  # 使用市价单
                    reason=f'超时平仓 (持仓{hold_duration.days}天)',
                    confidence=0.8
                )
            else:
                signal = self._create_signal(
                    signal_type='BUY',
                    quantity=abs(self.current_position),
                    price=None,  # 使用市价单
                    reason=f'超时平仓 (持仓{hold_duration.days}天)',
                    confidence=0.8
                )

            signals.append(signal)
            self._reset_position()
            self.last_trade_time = event.timestamp

        return signals

    def _reset_position(self):
        """重置持仓状态"""
        self.current_position = 0
        self.entry_price = 0.0
        self.entry_time = None

    def reset(self):
        """重置策略状态"""
        super().reset()
        self.detector.reset_state()
        self._reset_position()
        self.last_trade_time = None
        self.last_turn_point = None
        self.processed_turn_points = set()  # 重置已处理转折点集合
        self.signal_stats = {
            'total_signals': 0,
            'buy_signals': 0,
            'sell_signals': 0,
            'volume_confirmed': 0,
            'sr_confirmed': 0,
            'filtered_out': 0,
            'cooldown_filtered': 0
        }

    def get_stats(self) -> Dict[str, Any]:
        """获取策略统计信息"""
        base_stats = super().get_stats()

        base_stats.update({
            'current_position': self.current_position,
            'entry_price': self.entry_price,
            'entry_time': self.entry_time,
            'last_trade_time': self.last_trade_time,
            'signal_stats': self.signal_stats.copy(),
            'detector_stats': {
                'confirmed_points': len(self.detector.get_confirmed_points()),
                'sr_levels': len(self.detector.get_support_resistance_levels()),
                'volume_signals': len(self.detector.get_current_volume_signals())
            }
        })

        return base_stats

    def get_market_analysis(self) -> Dict[str, Any]:
        """获取当前市场分析"""
        return self.detector.get_market_analysis()
