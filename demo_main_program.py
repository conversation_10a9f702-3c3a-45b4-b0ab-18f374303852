#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示修改后的主程序功能
使用模拟数据展示逐日模拟和可视化效果
"""

import sys
import os
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_demo_data():
    """
    创建演示数据，模拟一个完整的牛熊周期
    """
    data = []
    start_date = datetime(2024, 1, 1)
    
    # 定义价格序列 - 一个完整的市场周期
    price_sequence = [
        # 第一阶段：稳步上涨 (1-30天)
        *[100 + i * 0.8 for i in range(30)],
        
        # 第二阶段：加速上涨 (31-50天)  
        *[124 + i * 1.2 for i in range(20)],
        
        # 第三阶段：高位震荡 (51-70天)
        *[148 + (-1)**i * (i % 5) * 0.8 for i in range(20)],
        
        # 第四阶段：开始下跌 (71-90天)
        *[150 - i * 1.5 for i in range(20)],
        
        # 第五阶段：加速下跌 (91-110天)
        *[120 - i * 2.0 for i in range(20)],
        
        # 第六阶段：筑底反弹 (111-130天)
        *[80 + i * 0.6 for i in range(20)],
        
        # 第七阶段：恢复上涨 (131-150天)
        *[92 + i * 1.0 for i in range(20)]
    ]
    
    # 转换为标准格式
    for i, price in enumerate(price_sequence):
        date = start_date + timedelta(days=i)
        
        # 添加日内波动
        daily_range = price * 0.02  # 2%的日内波动
        high = price + daily_range * 0.6
        low = price - daily_range * 0.4
        
        data.append({
            'date': date.strftime('%Y-%m-%d'),
            'open': price * 0.999,  # 开盘价略低于收盘价
            'high': high,
            'low': low,
            'close': price
        })
    
    return data

def run_simulation_demo():
    """
    运行逐日模拟演示
    """
    print("🚀 逐日模拟交易演示")
    print("=" * 50)
    
    # 创建演示数据
    demo_data = create_demo_data()
    print(f"创建演示数据: {len(demo_data)} 天")
    print(f"价格范围: {min(d['close'] for d in demo_data):.2f} - {max(d['close'] for d in demo_data):.2f}")
    
    # 导入模拟函数
    from points import simulate_daily_trading
    
    # 运行逐日模拟
    print(f"\n开始逐日模拟...")
    calculator, daily_values, signal_events = simulate_daily_trading(
        demo_data,
        window_size=30,
        turn_point_threshold=8.0,
        show_details=True  # 显示详细过程
    )
    
    # 分析结果
    print(f"\n📊 结果分析:")
    print(f"  数据周期: {len(demo_data)} 天")
    print(f"  信号事件: {len(signal_events)} 个")
    print(f"  价格涨幅: {(demo_data[-1]['close']/demo_data[0]['close']-1)*100:+.1f}%")
    
    strategy = calculator.trading_strategy
    performance = strategy.calculate_performance()
    print(f"  策略收益: {performance['total_return']:+.1f}%")
    print(f"  最终价值: {performance['portfolio_value']:,.0f}")
    
    return calculator, demo_data

def run_visualization_demo():
    """
    运行可视化演示
    """
    print(f"\n🎨 可视化演示")
    print("=" * 50)
    
    # 创建演示数据
    demo_data = create_demo_data()
    
    # 导入可视化函数
    from points import visualize_stock_trends
    
    print(f"正在生成交互式图表...")
    print(f"数据范围: {demo_data[0]['date']} 到 {demo_data[-1]['date']}")
    
    try:
        # 生成可视化
        visualize_stock_trends(
            demo_data,
            window_size=30,
            turn_point_threshold=8.0,
            enable_trading=True
        )
        print(f"✅ 可视化图表生成成功!")
        print(f"   请查看生成的 HTML 文件")
        
    except Exception as e:
        print(f"❌ 可视化生成失败: {e}")

def demonstrate_mode_switching():
    """
    演示模式切换功能
    """
    print(f"\n🔄 模式切换演示")
    print("=" * 50)
    
    # 创建演示数据
    demo_data = create_demo_data()
    
    # 导入必要函数
    from points import simulate_daily_trading, analyze_stock_trends
    
    print("1️⃣ 逐日模拟模式 (推荐)")
    sim_calc, daily_values, signal_events = simulate_daily_trading(
        demo_data, window_size=30, turn_point_threshold=8.0, show_details=False
    )
    sim_perf = sim_calc.trading_strategy.calculate_performance()
    
    print("2️⃣ 传统批量模式 (兼容)")
    trad_calc = analyze_stock_trends(
        demo_data, window_size=30, turn_point_threshold=8.0, enable_trading=True
    )
    trad_perf = trad_calc.trading_strategy.calculate_performance()
    
    print(f"\n📈 模式对比:")
    print(f"  逐日模拟: 收益={sim_perf['total_return']:+.1f}%, 交易={sim_perf['total_trades']}笔")
    print(f"  传统批量: 收益={trad_perf['total_return']:+.1f}%, 交易={trad_perf['total_trades']}笔")
    
    print(f"\n💡 推荐使用逐日模拟模式，因为:")
    print(f"  ✓ 避免未来数据依赖")
    print(f"  ✓ 真实模拟交易过程")
    print(f"  ✓ 支持实时风险管理")

def show_usage_instructions():
    """
    显示使用说明
    """
    print(f"\n📖 使用说明")
    print("=" * 50)
    
    print(f"修改后的 points.py 主程序支持两种运行模式:")
    print()
    
    print(f"🚀 逐日模拟模式 (推荐):")
    print(f"  - 设置 SIMULATION_MODE = True")
    print(f"  - 逐日处理数据，避免未来数据依赖")
    print(f"  - 提供完整的交易过程记录")
    print(f"  - 支持实时风险管理")
    print()
    
    print(f"📊 传统批量模式 (兼容):")
    print(f"  - 设置 SIMULATION_MODE = False")
    print(f"  - 保持原有的批量分析方式")
    print(f"  - 向后兼容，适合对比测试")
    print()
    
    print(f"🎨 可视化功能:")
    print(f"  - 保持原有的醒目可视化效果")
    print(f"  - 支持交互式图表")
    print(f"  - 显示转折点和交易信号")
    print()
    
    print(f"⚙️ 参数配置:")
    print(f"  - STOCK_CODE: 股票代码")
    print(f"  - START_DATE/END_DATE: 数据日期范围")
    print(f"  - WINDOW_SIZE: 趋势线计算窗口")
    print(f"  - TURN_POINT_THRESHOLD: 转折点识别阈值")
    print(f"  - SHOW_SIMULATION_DETAILS: 是否显示模拟详情")

if __name__ == "__main__":
    try:
        print("🎯 points.py 主程序功能演示")
        print("=" * 60)
        
        # 运行逐日模拟演示
        calculator, demo_data = run_simulation_demo()
        
        # 演示模式切换
        demonstrate_mode_switching()
        
        # 运行可视化演示
        run_visualization_demo()
        
        # 显示使用说明
        show_usage_instructions()
        
        print(f"\n🎉 演示完成!")
        print(f"points.py 主程序现在完全支持:")
        print(f"  ✅ 去除未来数据依赖")
        print(f"  ✅ 逐日模拟测试")
        print(f"  ✅ 保持醒目的可视化")
        print(f"  ✅ 灵活的模式切换")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
